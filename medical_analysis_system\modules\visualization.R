# 医学数据分析系统 - 可视化模块
# Medical Data Analysis System - Visualization Module

# 森林图绘制函数
create_forest_plot <- function(results_data, title = "Forest Plot", 
                              or_column = "OR", ci_lower_column = "CI_lower", 
                              ci_upper_column = "CI_upper", p_column = "p_value") {
  tryCatch({
    log_info("开始绘制森林图")
    
    library(forestplot)
    library(ggplot2)
    
    # 数据验证
    if (is.null(results_data) || nrow(results_data) == 0) {
      stop("森林图数据为空")
    }
    
    # 筛选显著结果
    significant_results <- results_data[results_data[[p_column]] < 0.05, ]
    
    if (nrow(significant_results) == 0) {
      log_warn("没有显著结果用于绘制森林图")
      return(NULL)
    }
    
    # 准备数据
    plot_data <- significant_results[order(significant_results[[or_column]]), ]
    
    # 创建标签文本
    labeltext <- cbind(
      c("Variable", plot_data$variable),
      c("OR (95% CI)", paste0(round(plot_data[[or_column]], 3), " (", 
                             round(plot_data[[ci_lower_column]], 3), "-",
                             round(plot_data[[ci_upper_column]], 3), ")")),
      c("P-value", format_pvalue(plot_data[[p_column]]))
    )
    
    # 创建森林图
    forest_plot <- forestplot(
      labeltext,
      mean = c(NA, plot_data[[or_column]]),
      lower = c(NA, plot_data[[ci_lower_column]]),
      upper = c(NA, plot_data[[ci_upper_column]]),
      title = title,
      zero = 1,
      boxsize = 0.3,
      graph.pos = 2,
      col = fpColors(box = "royalblue", line = "darkblue", zero = "gray50"),
      cex = 0.9,
      lineheight = "auto",
      colgap = unit(6, "mm"),
      lwd.ci = 2,
      ci.vertices = TRUE,
      ci.vertices.height = 0.4,
      clip = c(0.1, 10),
      xticks = c(0.1, 0.5, 1, 2, 5, 10),
      xlog = TRUE
    )
    
    log_info("森林图绘制完成")
    return(forest_plot)
    
  }, error = function(e) {
    log_error(paste("森林图绘制失败:", e$message))
    return(NULL)
  })
}

# ROC曲线绘制函数
create_roc_plot <- function(models_list, labels = NULL, title = "ROC Curves") {
  tryCatch({
    log_info("开始绘制ROC曲线")
    
    library(pROC)
    library(ggplot2)
    library(survminer)
    
    if (length(models_list) == 0) {
      stop("ROC曲线模型列表为空")
    }
    
    # 如果labels为空，创建默认标签
    if (is.null(labels)) {
      labels <- paste("Model", seq_along(models_list))
    }
    
    # 创建ROC对象列表
    roc_list <- list()
    auc_values <- numeric()
    
    for (i in seq_along(models_list)) {
      model <- models_list[[i]]
      if ("roc_object" %in% names(model)) {
        roc_list[[i]] <- model$roc_object
        auc_values[i] <- round(model$auc, 3)
      } else {
        log_warn(paste("模型", i, "缺少ROC对象"))
        next
      }
    }
    
    # 更新标签包含AUC值
    updated_labels <- paste0(labels, " (AUC = ", auc_values, ")")
    
    # 使用ggplot2绘制ROC曲线
    roc_plot <- ggroc(roc_list, aes = c("color"), legacy.axes = TRUE) +
      labs(
        x = "1 - Specificity",
        y = "Sensitivity",
        title = title,
        color = "Models"
      ) +
      scale_color_discrete(labels = updated_labels) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom",
        legend.title = element_text(size = 12),
        legend.text = element_text(size = 10),
        axis.title = element_text(size = 12),
        axis.text = element_text(size = 10),
        panel.grid.major = element_line(color = "gray90"),
        panel.grid.minor = element_blank()
      ) +
      geom_abline(intercept = 0, slope = 1, color = "gray", linetype = "dashed") +
      coord_fixed()
    
    log_info("ROC曲线绘制完成")
    return(roc_plot)
    
  }, error = function(e) {
    log_error(paste("ROC曲线绘制失败:", e$message))
    return(NULL)
  })
}

# 校准曲线绘制函数
create_calibration_plot <- function(model, data, outcome_var, 
                                   title = "Calibration Plot", n_groups = 10) {
  tryCatch({
    log_info("开始绘制校准曲线")
    
    library(rms)
    library(ggplot2)
    
    # 获取预测概率
    predicted_prob <- predict(model, type = "response")
    actual_outcome <- data[[outcome_var]]
    
    # 创建分组
    prob_groups <- cut(predicted_prob, breaks = quantile(predicted_prob, 
                      probs = seq(0, 1, length.out = n_groups + 1)), 
                      include.lowest = TRUE)
    
    # 计算每组的观察率和预测率
    calibration_data <- data.frame(
      group = levels(prob_groups),
      predicted = tapply(predicted_prob, prob_groups, mean, na.rm = TRUE),
      observed = tapply(actual_outcome, prob_groups, mean, na.rm = TRUE),
      n = table(prob_groups)
    )
    
    # 移除缺失值
    calibration_data <- calibration_data[complete.cases(calibration_data), ]
    
    # 绘制校准曲线
    calibration_plot <- ggplot(calibration_data, aes(x = predicted, y = observed)) +
      geom_point(aes(size = n), color = "steelblue", alpha = 0.7) +
      geom_smooth(method = "loess", se = TRUE, color = "red", linetype = "solid") +
      geom_abline(intercept = 0, slope = 1, color = "gray", linetype = "dashed") +
      labs(
        x = "Predicted Probability",
        y = "Observed Probability", 
        title = title,
        size = "Group Size"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom",
        axis.title = element_text(size = 12),
        axis.text = element_text(size = 10),
        panel.grid.major = element_line(color = "gray90"),
        panel.grid.minor = element_blank()
      ) +
      coord_fixed() +
      xlim(0, 1) +
      ylim(0, 1)
    
    log_info("校准曲线绘制完成")
    return(calibration_plot)
    
  }, error = function(e) {
    log_error(paste("校准曲线绘制失败:", e$message))
    return(NULL)
  })
}

# 决策曲线分析图
create_dca_plot <- function(models_list, data, outcome_var, 
                           title = "Decision Curve Analysis") {
  tryCatch({
    log_info("开始绘制决策曲线")
    
    library(rmda)
    library(ggplot2)
    
    # 创建决策曲线对象列表
    dca_list <- list()
    
    for (i in seq_along(models_list)) {
      model_name <- names(models_list)[i]
      if (is.null(model_name)) model_name <- paste("Model", i)
      
      model <- models_list[[i]]
      
      # 构建公式
      if ("formula" %in% names(model)) {
        formula_str <- model$formula
      } else {
        # 尝试从模型对象中提取公式
        formula_str <- paste(outcome_var, "~", 
                           paste(names(coef(model$model))[-1], collapse = " + "))
      }
      
      # 创建决策曲线
      dca_obj <- decision_curve(
        as.formula(formula_str),
        data = data,
        family = binomial(link = 'logit'),
        study.design = "cohort",
        thresholds = seq(0, 1, by = 0.01),
        confidence.intervals = 0.95
      )
      
      dca_list[[model_name]] <- dca_obj
    }
    
    # 绘制决策曲线
    dca_plot <- plot_decision_curve(
      dca_list,
      curve.names = names(dca_list),
      xlim = c(0, 1),
      ylim = c(-0.2, 1),
      confidence.intervals = FALSE,
      legend.position = "topright",
      cost.benefit.axis = FALSE
    )
    
    log_info("决策曲线绘制完成")
    return(dca_plot)
    
  }, error = function(e) {
    log_error(paste("决策曲线绘制失败:", e$message))
    return(NULL)
  })
}

# LASSO系数路径图
create_lasso_plot <- function(lasso_results, title = "LASSO Coefficient Path") {
  tryCatch({
    log_info("开始绘制LASSO系数路径图")
    
    library(glmnet)
    library(ggplot2)
    
    if (is.null(lasso_results$model)) {
      stop("LASSO结果中缺少模型对象")
    }
    
    # 创建系数路径图
    lasso_plot <- plot(lasso_results$model, xvar = "lambda", label = TRUE)
    
    # 添加最优lambda线
    if (!is.null(lasso_results$optimal_lambda)) {
      abline(v = log(lasso_results$optimal_lambda), col = "red", lty = 2)
    }
    
    log_info("LASSO系数路径图绘制完成")
    return(lasso_plot)
    
  }, error = function(e) {
    log_error(paste("LASSO系数路径图绘制失败:", e$message))
    return(NULL)
  })
}

# 缺失值模式图
create_missing_pattern_plot <- function(data, title = "Missing Data Pattern") {
  tryCatch({
    log_info("开始绘制缺失值模式图")
    
    library(VIM)
    library(ggplot2)
    
    # 计算缺失值比例
    missing_prop <- sapply(data, function(x) sum(is.na(x)) / length(x))
    missing_prop <- missing_prop[missing_prop > 0]
    
    if (length(missing_prop) == 0) {
      log_info("数据中无缺失值")
      return(NULL)
    }
    
    # 创建缺失值模式图
    missing_plot <- aggr(data, 
                        col = c('navyblue', 'red'),
                        numbers = TRUE,
                        sortVars = TRUE,
                        labels = names(data),
                        cex.axis = 0.7,
                        gap = 3,
                        ylab = c("Missing Data Histogram", "Pattern"))
    
    log_info("缺失值模式图绘制完成")
    return(missing_plot)
    
  }, error = function(e) {
    log_error(paste("缺失值模式图绘制失败:", e$message))
    return(NULL)
  })
}

# 相关性热图
create_correlation_heatmap <- function(data, title = "Correlation Heatmap") {
  tryCatch({
    log_info("开始绘制相关性热图")
    
    library(corrplot)
    library(ggplot2)
    library(reshape2)
    
    # 选择数值变量
    numeric_data <- data[sapply(data, is.numeric)]
    
    if (ncol(numeric_data) < 2) {
      log_warn("数值变量不足，无法绘制相关性热图")
      return(NULL)
    }
    
    # 计算相关系数
    cor_matrix <- cor(numeric_data, use = "complete.obs")
    
    # 转换为长格式
    cor_melted <- melt(cor_matrix)
    
    # 绘制热图
    correlation_plot <- ggplot(cor_melted, aes(Var1, Var2, fill = value)) +
      geom_tile(color = "white") +
      scale_fill_gradient2(low = "blue", high = "red", mid = "white", 
                          midpoint = 0, limit = c(-1, 1), space = "Lab",
                          name = "Correlation") +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text.x = element_text(angle = 45, vjust = 1, hjust = 1),
        axis.title.x = element_blank(),
        axis.title.y = element_blank(),
        panel.grid.major = element_blank(),
        panel.border = element_blank(),
        panel.background = element_blank(),
        axis.ticks = element_blank()
      ) +
      labs(title = title) +
      coord_fixed()
    
    log_info("相关性热图绘制完成")
    return(correlation_plot)
    
  }, error = function(e) {
    log_error(paste("相关性热图绘制失败:", e$message))
    return(NULL)
  })
}
