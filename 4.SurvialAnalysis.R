#加载所需要的包
library(survival)
library(ggplot2)
#install.packages('rms')
library(rms)  
library(ggrcs)
library(segmented)

rm(list = ls())
load(file = "ready.data.Rdata")
names(raw.ready)
merge.data <- raw.ready
dim(merge.data)
names(merge.data)

merge.data$icustay_detail.admission_age[merge.data$icustay_detail.admission_age > 89] <- 90

# 全局
data <- merge.data 
filepath <- "export/RCS/"

# 模型
# data <- dataInput
#filepathDate <-'export/2024-03-19-13-06-57'
#filepath <- paste0(filepathDate,'/RCS')

if(!dir.exists(filepath)){dir.create(filepath)}

# 导入示例数据
# 对数据进行打包，整理
dd <- datadist(data) #为后续程序设定数据环境
options(datadist='dd') #为后续程序设定数据环境






#### select all the sig var


#### LASSO  ####
print("LASSO analysis stating")
#install.packages("glmnet")
dataInput <- merge.data
colnames(dataInput)

x<- data.matrix(dataInput[,c(2:(length(dataInput)-2))]);anyNA(x)
y<- data.matrix(dataInput$status)

f1 = glmnet(x,y, family="binomial", nlambda=100, alpha=1) 

alpha1.fit <- cv.glmnet(x,y,type.measure = "class",alpha=1,family="binomial") ## type.measure = "class"

print(alpha1.fit)

#factors_exclude <- c("icustay_detail.los_icu","apsiii.apsiii","admin.length","sapsii.sapsii","sofa.sofa","apsiii.apsiii","oasis.oasis","time")
factors_exclude <- c()
lasso_coef <- as.data.frame(as.matrix(coef(f1,s=alpha1.fit$lambda.1se))) 
colnames(lasso_coef) <- 's1'
factorsInput <- row.names(lasso_coef)[lasso_coef$s1 != 0][-1];factorsInput
factorsInput <- factorsInput[!factorsInput %in% factors_exclude];factorsInput
#### end with lasso ####



############# 
var_test = "icustay_detail.admission_age"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(icustay_detail.admission_age,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, icustay_detail.admission_age,fun=exp,ref.zero = TRUE)
head(HR)


fit1 <-coxph(Surv(time,status==1) ~ icustay_detail.admission_age,data=data)
library(segmented)
os<-segmented(fit1, ~icustay_detail.admission_age, psi=40) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(icustay_detail.admission_age,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(icustay_detail.admission_age,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=0.5)+
  geom_vline(xintercept=cut_point,color = '#d40e8c', linetype=2,size=0.5)+ #查表HR=1对应的age
  geom_text(aes(x = cut_point+1, y = 0, label = cut_point),  colour = "black", size = 3)+
  labs(title = "RCS", x="Age", y="HR (95%CI)")
dev.off()
# 绘图


# 拟合模型
var_test = "icustay_detail.los_hospital"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(icustay_detail.los_hospital,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, icustay_detail.los_hospital,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ icustay_detail.los_hospital,data=data)

os<-segmented(fit1, ~icustay_detail.los_hospital, psi=40) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(icustay_detail.los_hospital,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(icustay_detail.los_hospital,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point+2, y = 0, label = cut_point), vjust = -0.5, colour = "#BB0000", size = 3)+
  labs(title = "RCS", x="icustay_detail.los_hospital", y="HR (95%CI)")
dev.off()
# 绘图





############# 
# 拟合模型
var_test = "apsiii.apsiii"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(apsiii.apsiii,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, apsiii.apsiii,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ apsiii.apsiii,data=data)

os<-segmented(fit1, ~apsiii.apsiii, psi=40) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(apsiii.apsiii,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(apsiii.apsiii,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point+2, y = 0, label = cut_point), vjust = -0.5, colour = "#BB0000", size = 3)+
  labs(title = "RCS", x="apsiii.apsiii", y="HR (95%CI)")
dev.off()
# 绘图





############# 
# 拟合模型
var_test = "labsfirstday_mean.hematocrit_avg"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(labsfirstday_mean.hematocrit_avg,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, labsfirstday_mean.hematocrit_avg,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ labsfirstday_mean.hematocrit_avg,data=data)

os<-segmented(fit1, ~labsfirstday_mean.hematocrit_avg, psi=40) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(labsfirstday_mean.hematocrit_avg,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(labsfirstday_mean.hematocrit_avg,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point+2, y = 0, label = cut_point), vjust = -0.5, colour = "#BB0000", size = 3)+
  labs(title = "RCS", x="labsfirstday_mean.hematocrit_avg", y="HR (95%CI)")
dev.off()
# 绘图





############# 
# 拟合模型
var_test = "labsfirstday_mean.hemoglobin_avg"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(labsfirstday_mean.hemoglobin_avg,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, labsfirstday_mean.hemoglobin_avg,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ labsfirstday_mean.hemoglobin_avg,data=data)

os<-segmented(fit1, ~labsfirstday_mean.hemoglobin_avg) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(labsfirstday_mean.hemoglobin_avg,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(labsfirstday_mean.hemoglobin_avg,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point, y = 0, label = cut_point), vjust = -0.5, colour = "#BB9966", size = 3)+
  labs(title = "RCS", x="labsfirstday_mean.hemoglobin_avg", y="HR (95%CI)")
dev.off()
# 绘图





############# 
# 拟合模型
var_test = "labsfirstday_mean.aniongap_avg"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(labsfirstday_mean.aniongap_avg,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, labsfirstday_mean.aniongap_avg,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ labsfirstday_mean.aniongap_avg,data=data)

os<-segmented(fit1, ~labsfirstday_mean.aniongap_avg) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(labsfirstday_mean.aniongap_avg,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(labsfirstday_mean.aniongap_avg,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point, y = 0, label = cut_point), vjust = -0.5, colour = "#BB9966", size = 3)+
  labs(title = "RCS", x="labsfirstday_mean.aniongap_avg", y="HR (95%CI)")
dev.off()
# 绘图


############# 
# 拟合模型
var_test = "vitals_first_day.resprate_mean"
# 拟合模型
fit<- cph(Surv(time,status==1) ~ rcs(vitals_first_day.resprate_mean,4) ,data=data)  # 节点数设为4
# 非线性检验
# P<0.05为存在非线性关系
# anova(fit)

# 查看HR预测表
# 看一下预测的HR所对因的age
HR<-Predict(fit, vitals_first_day.resprate_mean,fun=exp,ref.zero = TRUE)
# head(HR)

fit1 <-coxph(Surv(time,status==1) ~ vitals_first_day.resprate_mean,data=data)

os<-segmented(fit1, ~vitals_first_day.resprate_mean) #estimate the breakpoint in the age effect
#summary(os) #actually it means summary.coxph(os)
#plot.segmented(os) #call explicitly plot.segmented() to plot the fitted piecewise lines
cut_point <- round(data.frame(os$psi)$Est., 2)
cut_point


pdf(paste0(filepath,var_test,".pdf"))
ggplot()+
  geom_line(data=HR, aes(vitals_first_day.resprate_mean,yhat),
            linetype="solid",size=1,alpha = 0.7,colour="#0070b9")+
  geom_ribbon(data=HR, 
              aes(vitals_first_day.resprate_mean,ymin = lower, ymax = upper),
              alpha = 0.1,fill="#0070b9")+
  theme_classic()+
  geom_hline(yintercept=1, linetype=2,size=1)+
  #geom_vline(xintercept=cut_point,size=0.5,color = '#d40e8c')+ #查表HR=1对应的age
  geom_vline(aes(xintercept = cut_point), colour = "#BB0000", linetype = "dashed")+
  geom_text(aes(x = cut_point, y = 0, label = cut_point), vjust = -0.5, colour = "#BB9966", size = 3)+
  labs(title = "RCS", x="vitals_first_day.resprate_mean", y="HR (95%CI)")
dev.off()
# 绘图
