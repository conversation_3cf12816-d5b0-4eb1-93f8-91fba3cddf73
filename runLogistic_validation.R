if(!dir.exists(filepath)){dir.create(filepath)}
factorsInput = finalVar
age_m = "icustay_detail.admission_age"


mul_glm <- glm(as.formula(paste0("status==1~",factorsInput)), data = dataInput, family = binomial())
library(broom)
OR <- round(exp(coef(mul_glm)),2)
ci95 <- paste0(sprintf("%.2f", exp(confint(mul_glm))[,1]), "-",sprintf("%.2f", exp(confint(mul_glm))[,2]))
p.value <- round(tidy(mul_glm)$p.value,2)
p.star <- ifelse(p.value<0.001,"***",ifelse(p.value<0.01,"**",ifelse(p.value<0.05,"*","")))

mul_glim_model <- data.frame('OR'= OR,'CI' = ci95,'p' = p.value,'p.star'= p.star)[-1,]
write.csv(mul_glim_model,file = paste0(filepath,"/1.2 multi_logistic_Summary.csv"),row.names = T)

#### calculate c-index ####
library(Hmisc)
Cindex <- rcorrcens(dataInput$status~predict(mul_glm))
SE <- Cindex[4]/2
ci95 <- paste0(round(Cindex[1]+1.96*SE,2),'-',round(Cindex[1]-1.96*SE,2));
c_index <- data.frame("C value"=Cindex[1],
                      "ci95" = ci95,
                      "p value" =Cindex[6] 
);c_index
write.table(c_index,file = paste0(filepath,"/2. c_index_logistic.txt"),sep = "\t",col.names = T,row.names = F)


source('runROC_DCA.R')

