rm(list = ls())
# load old 

load("E:/1_WorkFile/MIMIC/17. Perforation of intestine/R/export/2023-12-15-13-42-46--/merge.data.grouped.Rdata")


# 将指定列转换为因子，并使因子水平从0开始
for (i in c(2, 5, 37:43)) {
  merge.data[[i]] <- as.factor(merge.data[[i]])
  levels(merge.data[[i]]) <- 0:(length(levels(merge.data[[i]]))-1)
}

cohort.dev <- subset(merge.data, merge.data$anaGroup == "training cohort")
cohort.val <- subset(merge.data, merge.data$anaGroup == "validation cohort")

max_col_num = length(merge.data);


if(!dir.exists('export')) {dir.create('export')}
filepathDate <- paste0("export/", today)
if(!dir.exists(filepathDate)) {dir.create(filepathDate)}


dataInput <- cohort.dev
filepath <- paste0(filepathDate, "/cohort_development")
source('runLogistic_development.R')


dataInput <- cohort.val
filepath <- paste0(filepathDate, "/cohort_validation")
source('runLogistic_validation.R')
