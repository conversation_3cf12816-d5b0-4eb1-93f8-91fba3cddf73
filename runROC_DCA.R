####### initial varibles   ##########
nomal_var_forDCA = 'icustay_detail.admission_age'
comparing_scales = c('patients.gender','sapsii.sapsii',"sofa.sofa",'apsiii.apsiii')

#### ROC analysis ####
#install.packages("pROC")
library("pROC")
#install.packages("ResourceSelection")
library(ResourceSelection) 

mul_glm <- glm(as.formula(paste0("status==1~",finalVar)), data = dataInput, family = "binomial")
pre <- predict(mul_glm,type='response')
roc.obj0 <- roc(dataInput$status,pre)
auc0 <- round(auc(dataInput$status,pre),4)

roc_lables <- c(paste("28-days ROC for nomogram, AUC=", auc0))

cutoff1<-coords(roc.obj0, "best")
rocValues <- data.frame("model"="nomogram",
                        "AUCvalue" = as.character(auc0) , 
                        "ci95"=paste0(round(ci.auc(roc.obj0)[1],3),"-",round(ci.auc(roc.obj0)[3],3)),
                        'cutoff'=as.character(cutoff1[1]),
                        "cutoff_specificity"=as.character(cutoff1[2]),
                        "cutoff_sensitivity"=as.character(cutoff1[3])
)
write.table(rocValues,file = paste0(filepath,"/2. AUC_value_logistic.txt"),sep = "\t",col.names = T,row.names = F)




### for loop to get each roc object #######
for (i in seq_along(comparing_scales)) {
  scale <- comparing_scales[i]
  print(paste('constructing glm model for', scale))
  formula <- as.formula(paste0("status == 1 ~ ", scale))
  glm_model <- glm(formula, data = dataInput, family = binomial())
  pre <- predict(glm_model)
  roc.obj <- roc(dataInput$status, pre)
  auc <- round(auc(dataInput$status, pre), 4)
  
  label <- paste("28-days ROC for", scale, ", AUC=", auc)
  roc_lables <- c(roc_lables, label)
  
  roc_obj_name <- paste0("roc.obj", i)
  assign(roc_obj_name, roc.obj)

  auc_obj_name <- paste0("auc", i)
  assign(auc_obj_name, auc)
}

roc.list <- list(roc.obj0,roc.obj1,roc.obj2,roc.obj3)
pdf(paste0(filepath,"/5. ROC.logtistic.pdf"))
p<-ggroc(roc.list, aes = c("color"), legacy.axes = TRUE)+ 
  labs(x = "1 - Specificity", y = "Sensitivity", col ="Models") +
  scale_color_discrete(labels=roc_lables)+
  theme_bw() +
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank())+
  geom_abline(color="gray",linetype="dashed")+
  theme(legend.position = c(0.7,0.15))
plot(p)
dev.off()


#### 校准曲线 ####
fit2<-lrm(as.formula(paste0("status~",finalVar)),data=dataInput,x=T,y=T) 
pred.logit <- predict(fit2)
phat <- 1/(1+exp(-pred.logit))

pdf(paste0(filepath,"/6. cal.logtistic.pdf"))
val.prob(phat, dataInput$status)  # subgroups of 20 obs.
dev.off()

#cal1 <- calibrate(fit, method='boot', B=1000)
#plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))

hl <- hoslem.test(mul_glm$y,fitted(mul_glm),g=10)
hl2 <- data.frame(hl$statistic,hl$p.value)
write.table(hl2,file =paste0(filepath, "/6.1. hosmer value.txt"),row.names = F)
#### DCA curve old ####


getCompareModel <- function(testVariable){
  source("stdca.R")
  library("survival")
  library("nricens")
  # testVariable = 'apsiii.apsiii'
  ## glm fit (logistic model)
  mstd = glm(as.formula(paste("status==1~",testVariable,"+",nomal_var_forDCA)), binomial(logit), data=dataInput, x=TRUE)
  mnew = glm(as.formula(paste0("status==1~",finalVar)), binomial(logit), data=dataInput, x=TRUE)
  ## Calculation of risk difference NRI
  nri<-nribin(mdl.std = mstd, mdl.new = mnew, cut = 0.05, niter = 200,updown = 'diff')
  z=abs(nri$nri[1,1]/nri$nri[1,2])
  pvalue = (1-pnorm(z))*2
  nri <- cbind(nri$nri[1,],pvalue)
  write.table(nri, file = paste0(filepath,"/8.",testVariable,".NRI.txt"),sep="\t")
  
  #install.packages("PredictABEL")
  library(PredictABEL)
  pstd=fitted(mstd)
  pnew=fitted(mnew)
  cOutcome = dim(dataInput)[2]-1
  #reclassification(data = dataInput,cOutcome = 46,predrisk1 = pstd,predrisk2 = pnew,cutoff = c(0,0.2,0.4,1))
  #### important the COLUNM NUMBER should be changed  of the "cOutcome" ### (dim(dataInput))
  results <- capture.output(reclassification(data = dataInput,cOutcome = cOutcome,predrisk1 = pstd,predrisk2 = pnew,cutoff = c(0,0.2,0.4,1)))
  write.table(results[length(results)], file = paste0(filepath,"/9.", testVariable, ".IDI.txt"),row.names = F,sep="\t", col.names = F)
}

for (i in comparing_scales) {
  getCompareModel(i)
}

# #### decision curve ####
#install.packages("rmda")
library(rmda)
# #first use rmda with the default settings (set bootstraps = 50 here to reduce computation time). 
#dataInput<-dcaData
nomogram.model <- decision_curve(as.formula(paste0("status~",finalVar)),
                                 data = dataInput,
                                 family = binomial(link ='logit'),
                                 study.design = "cohort",
                                 thresholds = seq(0,1, by = 0.01),
                                 confidence.intervals= 0.95)

model_list <- list('nomogram'=nomogram.model)
models <- comparing_scales
for (model in models) {
  
  formula <- as.formula(paste0("status ~ ", model))
  model_list[[model]] <-decision_curve(formula, 
                               data = dataInput, 
                               study.design = "cohort", 
                               thresholds = seq(0, 1, by = 0.01),
                               confidence.intervals = 0.95,
                               policy = "opt-in", 
                               bootstraps = 50)

}
# 
#plot_decision_curve(baseline.model,  curve.names = "baseline model")
pdf(paste0(filepath,"/7. decisionCurve.pdf"))
#since we want to plot more than one curve, we pass a list of 'decision_curve' objects to the plot
plot_decision_curve( model_list,
                     curve.names = c("nomogram",models), xlim = c(0, 1), ylim=c(-0.2,1),
                     confidence.intervals = FALSE,  #remove confidence intervals
                     legend.position = "topright",cost.benefit.axis =FALSE)
#plot_decision_curve(full.model,  curve.names = "full model",cost.benefit.axis =FALSE)
dev.off() 
