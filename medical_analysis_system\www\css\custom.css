/* 医学数据分析系统 - 自定义样式 */
/* Medical Data Analysis System - Custom CSS */

/* 全局样式 */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* 页面头部样式 */
.page-header {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.page-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片样式增强 */
.box {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
}

.box:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.box-header {
  border-radius: 10px 10px 0 0;
}

.box-body {
  border-radius: 0 0 10px 10px;
}

/* 按钮样式增强 */
.btn {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 值框样式 */
.small-box {
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.small-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.small-box .icon {
  transition: all 0.3s ease;
}

.small-box:hover .icon {
  transform: scale(1.1);
}

/* 表格样式 */
.dataTables_wrapper {
  margin-top: 20px;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
  margin: 10px 0;
}

.table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-weight: 600;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* 文件上传区域 */
.file-upload-area {
  border: 2px dashed #007bff;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
}

.file-upload-area:hover {
  border-color: #0056b3;
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
  transform: scale(1.02);
}

.file-upload-area.dragover {
  border-color: #28a745;
  background: linear-gradient(135deg, #f0fff4 0%, #d4edda 100%);
}

/* 进度条样式 */
.progress {
  height: 8px;
  border-radius: 10px;
  background-color: #e9ecef;
  overflow: hidden;
}

.progress-bar {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.6s ease;
}

/* 标签页样式 */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
  border: none;
  border-radius: 25px 25px 0 0;
  margin-right: 5px;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* 表单控件样式 */
.form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  transform: scale(1.02);
}

.form-group label {
  font-weight: 600;
  color: #495057;
}

/* 选择框样式 */
.selectize-input {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.selectize-input.focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 复选框和单选框样式 */
.checkbox input[type="checkbox"],
.radio input[type="radio"] {
  transform: scale(1.2);
  margin-right: 8px;
}

/* 警告和提示样式 */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
}

/* 加载动画 */
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* 图表容器样式 */
.plot-container {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
}

/* 侧边栏样式增强 */
.main-sidebar {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-menu > li > a {
  transition: all 0.3s ease;
}

.sidebar-menu > li > a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .page-header h1,
  .page-header h2 {
    font-size: 1.5rem;
  }
  
  .box {
    margin-bottom: 20px;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .small-box {
    margin-bottom: 20px;
  }
}

/* 打印样式 */
@media print {
  .main-sidebar,
  .main-header,
  .btn,
  .no-print {
    display: none !important;
  }
  
  .content-wrapper {
    margin-left: 0 !important;
  }
  
  .box {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
