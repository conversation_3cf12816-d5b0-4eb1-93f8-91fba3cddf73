rm(list = ls())

path <- "../data/mimic-iv/"
files <- list.files(path=path,pattern="*.csv")

##raw.import<-list()

for (i in 1:length(files)) {
  name<-gsub(".csv","",files[i])
  assign(name,read.csv(file(paste0(path,files[i])),sep = "\t",header = T,stringsAsFactors = F))
  ##raw.import[[name]]<-read.csv(file(paste0(path,files[i])),sep = "\t",header = T,stringsAsFactors = F)
}
##names(raw.import)

table( is.na(first_day_weight$first_day_weight.weight)) 
# over 50% delete
table(is.na(first_day_height$first_day_height.height))
# over 50% delete
##### end ####


######### icustay lost 5 patients that not entered ICU ###
merge.data <- dplyr::left_join(icustay_detail,sapsii)
merge.data <- dplyr::left_join(merge.data,first_day_sofa)
merge.data <- dplyr::left_join(merge.data,apsiii)
merge.data <- dplyr::left_join(merge.data,first_day_gcs)
merge.data <- dplyr::left_join(merge.data,first_day_weight)
merge.data <- dplyr::left_join(merge.data,first_day_height)
merge.data <- dplyr::left_join(merge.data,first_day_lab_mean)
merge.data <- dplyr::left_join(merge.data,first_day_urine_output)
merge.data <- dplyr::left_join(merge.data,first_day_vitalsign)
merge.data <- dplyr::left_join(merge.data,elixhauser_ahrq_v37)



colnames(merge.data);dim(merge.data) # 476 106 



#### exclusion start ######
### 1. orignial icustay_id : 724
table(merge.data$icustay_detail.admission_age)
merge.data <- merge.data[merge.data$icustay_detail.admission_age > 18,]
dim(merge.data)
### 2. age > 18 : 476     --------->excluded 0

### exclude patients less than 24 hours
d_less_24 <- merge.data[merge.data$icustay_detail.los_icu <1,]; dim(d_less_24); # 57 106
merge.data <- merge.data[!(merge.data$stay_id %in% d_less_24$stay_id),]
dim(merge.data)
### 3. los.icu > 1 : 419  --------->excluded 57

### BMI
#merge.data$BMI = merge.data$first_day_weight.weight/(merge.data$height.height*merge.data$height.height)
# height 缺失太严重，放弃
#### end exclusion ######

#### handle death ###
merge.data$time <- time_length(interval(parse_date_time(merge.data$icustay_detail.admittime,"Ymd HMS",truncated = 3) 
                                              ,parse_date_time(merge.data$icustay_detail.dod,"Ymd HMS",truncated = 3)),'day')
merge.data$time <- ifelse(is.na(merge.data$time),28,merge.data$time)
#merge.data$days.death <- ifelse(merge.data$days.death >= 30,30,merge.data$days.death)
merge.data$status <- ifelse(merge.data$time>=28 |is.na(merge.data$time) ,0,1)



save(merge.data,file = "merge.data.Rdata")
#install.packages("mice")
#library(mice)


#### start handling missing data with mice package ####
rm(list = ls())
load(file = "merge.data.Rdata") 

aggr_plot <- aggr(merge.data, col=c('navyblue','red'),
                  numbers=TRUE,
                  sortVars=TRUE,
                  labels=names(data),
                  cex.axis=.7,
                  gap=3,
                  ylab=c("数据缺失模式直方图","模式"))

## delete columns with data lost more than 20%
missings <- aggr_plot$missings
missings$percentage <- missings$Count/dim(merge.data)[1]
vars_to_drop <- missings$Variable[missings$percentage > 0.2]
merge.data<- merge.data[,-which(names(merge.data)%in%vars_to_drop)]
colnames(merge.data);dim(merge.data) # 419  78
## check again
aggr_plot <- aggr(merge.data, col=c('navyblue','red'),
                  numbers=TRUE,
                  sortVars=TRUE,
                  labels=names(data),
                  cex.axis=.7,
                  gap=3,
                  ylab=c("数据缺失模式直方图","模式"))



missingVariables <- aggr_plot$missings$Variable[which(aggr_plot$missings$Count>0)]
noNAVariables <- aggr_plot$missings$Variable[which(aggr_plot$missings$Count==0)]


mice.data <- mice( merge.data[,missingVariables],m=5,method = "pmm",maxit = 100,seed = 3)
micedData <- complete(mice.data,3)
raw.ready <- cbind(merge.data[,noNAVariables],micedData)
colnames(raw.ready)

anyNA(raw.ready)
dim(raw.ready)


raw.ready <- raw.ready[,c(3,4,8,9,10,15,17:50,53:length(raw.ready),51:52)] 

save(raw.ready,file = "ready.data.Rdata");names(raw.ready)

