if(!dir.exists(filepath)){dir.create(filepath)}
cOutcome = dim(dataInput)[2]-1
age_m = "icustay_detail.admission_age"

anyNA(dataInput)

# 找出只有一个唯一值的列
single_value_columns <- sapply(dataInput[,c(1:cOutcome)], function(col) length(unique(col)) == 1)

# 提取只有一个唯一值的列名
single_value_column_names <- names(single_value_columns)[single_value_columns]
excluded_var <- c('elixhauser_ahrq_v37.hypertension', 'elixhauser_ahrq_v37.hypothyroidism', 
                  'elixhauser_ahrq_v37.coagulopathy','elixhauser_ahrq_v37.diabetes_uncomplicated',
                  'urine_output_first_day.urineoutput',
                  'elixhauser_ahrq_v37.peripheral_vascular','elixhauser_ahrq_v37.solid_tumor')
#single_value_column_names <- c(single_value_column_names, excluded_var)
single_value_column_names <- c(single_value_column_names)

# 打印结果
print(single_value_column_names)

#colnames(dataInput)
covariates <- colnames(dataInput)[2:(length(dataInput)-3)];covariates

#

# 创建 covariates 列表，不包括 'elixhauser_ahrq_v37.coagulopathy'
covariates <- covariates[!covariates %in% single_value_column_names]
dataInput <- dataInput[, !(names(dataInput) %in% single_value_column_names)]

## 单因素分析
print("uni_glm analysis stating")

uni_glim_model <-
  function(x){
    FML <- as.formula(paste0("status==1~",x))
    glm1<-glm(FML,data=dataInput,family= binomial)
    glm2<-summary(glm1)
    variables <- glm2$aliased[-1]
    OR <- round (exp(coef(glm1)),2)
    SE <- glm2$coefficients[,2]
    CI5 <- round(exp(coef(glm1)-1.96*SE),3)
    CI95 <- round(exp(coef(glm1)+1.96*SE),3)
    CI <-paste0(CI5,'-',CI95)
    p.value <- round(glm2$coefficients[,4],2)
    p.star <- ifelse(p.value<0.001,"***",ifelse(p.value<0.01,"**",ifelse(p.value<0.05,"*","")))
    uni_glim_model <- data.frame(
                                 'OR'= OR,
                                 'CI' = CI,
                                 'p' = p.value,
                                 'p.star'= p.star)[-1,]
    uni_glim_model$variables <- row.names(uni_glim_model)
    
    return(uni_glim_model[,c(5,1:4)])
  }

uni_glm <- lapply(covariates,uni_glim_model)
#library(plyr)
uni_glm <-ldply(uni_glm,data.frame);uni_glm
write.csv(uni_glm,file = paste0(filepath,"/1.1 single_logistic_Summary.csv"),row.names = T)


#### LASSO  ####
print("LASSO analysis stating")
#install.packages("glmnet")
colnames(dataInput)

x<- data.matrix(dataInput[,c(2:(length(dataInput)-2))]);anyNA(x)
y<- data.matrix(dataInput$status)

f1 = glmnet(x,y, family="binomial", nlambda=100, alpha=1) 

pdf(paste0(filepath,"/1.1 LASSO.coef.pdf"))
plot(f1, xvar="lambda", label=TRUE)
dev.off()


alpha1.fit <- cv.glmnet(x,y,type.measure = "class",alpha=1,family="binomial") ## type.measure = "class"
pdf(paste0(filepath,"/1.2 LASSO.log.pdf"))
plot(alpha1.fit)
dev.off()

print(alpha1.fit)

factors_exclude <- c("icustay_detail.los_icu","icustay_detail.los_hospital","admin.length","sapsii.sapsii","sofa.sofa","apsiii.apsiii","oasis.oasis","time")

lasso_coef <- as.data.frame(as.matrix(coef(f1,s=alpha1.fit$lambda.1se))) 
colnames(lasso_coef) <- 's1'
factorsInput <- row.names(lasso_coef)[lasso_coef$s1 != 0][-1];factorsInput
factorsInput <- factorsInput[!factorsInput %in% factors_exclude];factorsInput

factorsInput <- paste0(factorsInput,collapse = "+");factorsInput
factorsInput <- ifelse(nchar(factorsInput)==0, 'icustay_detail.admission_age',factorsInput);factorsInput

#### end with lasso ####

#### start with variables from univariable ####
# uni_glm[uni_glm$p<0.05,]

#### start multivariable analysis ####
print("multivariable analysis stating")
mul_glm <- glm(as.formula(paste0("status==1~",factorsInput)), data = dataInput, family = binomial())
library(broom)
OR <- round(exp(coef(mul_glm)),4)
ci95 <- paste0(sprintf("%.5f", exp(confint(mul_glm))[,1]), "-",sprintf("%.5f", exp(confint(mul_glm))[,2]))
p.value <- round(tidy(mul_glm)$p.value,2)
p.star <- ifelse(p.value<0.001,"***",ifelse(p.value<0.01,"**",ifelse(p.value<0.05,"*","")))

mul_glim_model <- data.frame('OR'= OR,'CI' = ci95,'p' = p.value,'p.star'= p.star)[-1,]
write.csv(mul_glim_model,file = paste0(filepath,"/1.2 multi_logistic_Summary.csv"),row.names = T)


#####
# 提取你的数据
significant_rows <- subset(mul_glim_model, p < 0.05)
write.csv(significant_rows,file = paste0(filepath,"/1.3 multi_significant_rows.csv"),row.names = T)

labeltext <- cbind(rownames(significant_rows), significant_rows$CI, significant_rows$p)

mean <- significant_rows$OR
lower <- sapply(strsplit(as.character(significant_rows$CI), split = "-"), function(x) as.numeric(x[1]))
upper <- sapply(strsplit(as.character(significant_rows$CI), split = "-"), function(x) as.numeric(x[2]))

# 加载绘图库
library(forestplot)
# 绘制森林图
pdf(file = paste0(filepath, "/1.2_multi_logistic_ForestPlot.pdf"))
forestplot(
  labeltext,
  mean = mean,
  lower = lower,
  upper = upper,
  zero = 1,               # 设置零线或参考线为HR=1，这是x轴的垂直线
  boxsize = 0.1,          # 设置小方块的大小
  graph.pos = 2,           # 指定森林图应该插入到图形中的位置，这里是第2列
  col = fpColors(box = "black", lines = "black", zero = "gray50"),
  cex = 0.2,
  lineheight = "auto",
  colgap = unit(6, "mm"),
  lwd.ci = 1,
  ci.vertices = TRUE,
  ci.vertices.height = 0.2
)
dev.off()


#### calculate c-index ####
print("calculate c-index stating")
library(Hmisc)
Cindex <- rcorrcens(dataInput$status~predict(mul_glm))
SE <- Cindex[4]/2
ci95 <- paste0(round(Cindex[1]+1.96*SE,2),'-',round(Cindex[1]-1.96*SE,2));
c_index <- data.frame("C value"=Cindex[1],
                      "ci95" = ci95,
                      "p value" =Cindex[6] 
                      );c_index
write.table(c_index,file = paste0(filepath,"/2. c_index_logistic.txt"),sep = "\t",col.names = T,row.names = F)

print(c_index)

### nomogram analysis ####
#加载诺模图和cox相关包
print("nomogram starting")
library(rms)
library(foreign)
library(survival)

#logistic模型多因素分析-进行比例风险假设检验
mul_glim_model[mul_glim_model$p<0.05,]

## select the variables in mul_glm
match_vector <- character()
for (j in seq_along(rownames(mul_glim_model))) {
  row_name <- rownames(mul_glim_model)[j]
  string1 <- colnames(mul_glm$model)[c(2:length(colnames(mul_glm$model)))]
  #string1 <- names(mul_glm$xlevels)
  #print(row_name)
  for (i in seq_along(string1)) {
    if (grepl(string1[i], row_name)) {
      match_vector[j] <- string1[i]
      #print('yes')
      break  # Exit the loop once a match is found
    }else{
      match_vector[j] <- row_name

    }
  }
}

## 再次转回factor


#any(is.na(dataInput)) 来检查整个数据框中是否存在缺失值，或者使用 colSums(is.na(dataInput)) 来检查每列中缺失值的数量。
any(is.na(dataInput))
colSums(is.na(dataInput))

#finalVar <- match_vector[mul_glim_model$p<0.05]
finalVar <- match_vector[mul_glim_model$p<0.05]
finalVar <- paste0(finalVar,collapse = "+");finalVar

#finalVar <- "icustay_detail.los_icu+sofa.sofa+oasis.oasis+apsiii.apsiii+com_hypertension+com_renal_failure+com_liver_disease+com_aids1+obesity+com_heart_disease1+labsfirstday_mean.chloride_avg+labsfirstday_mean.wbc_avg+urine_output_first_day.urineoutput+vitals_first_day.resprate_mean+gcs_first_day.mingcs"
ddist <- datadist(dataInput)
options(datadist="ddist")

#finalVar <-'icustay_detail.admission_age+gcs_first_day.mingcs+labsfirstday_mean.aniongap_avg+labsfirstday_mean.creatinine_avg+labsfirstday_mean.bun_avg+labsfirstday_mean.wbc_avg+urine_output_first_day.urineoutput+vitals_first_day.heartrate_mean+vitals_first_day.diasbp_mean+vitals_first_day.resprate_mean'

fit1<-lrm(as.formula(paste0("status == 1~ ",finalVar)),data=dataInput,x=T,y=T) 

nom <- nomogram(fit1, fun=plogis,lp=F,
                funlabel=c("Diagnositc possibility"))
pdf(paste0(filepath,"/3. nomogram.pdf"))
plot(nom, xfrac=.2)
dev.off()

######## dynamic nomogram
#install.packages("DynNom") #安装包；
#library(DynNom) #加载包；
#mul_glm <- glm(as.formula(paste0("status==1~",finalVar)), data = dataInput, family = "binomial")
#DynNom(mul_glm) #生成动态列线图（Dynamic Nomogram）
#install.packages("htmltools",update=T)

source('runROC_DCA.R')



