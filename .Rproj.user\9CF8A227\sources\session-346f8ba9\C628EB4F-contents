rm(list = ls())

path <- "../data/"
files <- list.files(path=path,pattern="*.csv")

##raw.import<-list()

for (i in 1:length(files)) {
  name<-gsub(".csv","",files[i])
  assign(name,read.csv(file(paste0(path,files[i])),sep = "\t",header = T,stringsAsFactors = F))
  ##raw.import[[name]]<-read.csv(file(paste0(path,files[i])),sep = "\t",header = T,stringsAsFactors = F)
}
##names(raw.import)

#table( is.na(weight_first_day$weight_first_day.weight_admit)) 
# over 50% delete
#table(is.na(height_first_day$height_first_day.height))

##### end ####


######### icustay lost 5 patients that not entered ICU ###

merge.data <- dplyr::left_join(icustay_detail,admissions)
merge.data <- dplyr::left_join(merge.data,patients)

merge.data <- dplyr::left_join(merge.data,sapsii)
merge.data <- dplyr::left_join(merge.data,sofa)
#merge.data <- dplyr::left_join(merge.data,first_day_sofa)
merge.data <- dplyr::left_join(merge.data,apsiii)
merge.data <- dplyr::left_join(merge.data,gcs_first_day)


#merge.data <- dplyr::left_join(merge.data,age)
#merge.data <- dplyr::left_join(merge.data,weight_first_day)


merge.data <- dplyr::left_join(merge.data,labsfirstday_mean)
merge.data <- dplyr::left_join(merge.data,urine_output_first_day)
merge.data <- dplyr::left_join(merge.data,vitals_first_day)


# elixhauser
merge.data <- dplyr::left_join(merge.data,elixhauser_ahrq_v37)


### data 
#### handle death ###
merge.data$days.death <- time_length(interval(parse_date_time(merge.data$admissions.admittime,"Ymd HMS",truncated = 3) 
                                              ,parse_date_time(merge.data$patients.dod,"Ymd HMS",truncated = 3)),'day')
merge.data$days.death <- ifelse(is.na(merge.data$days.death),28,merge.data$days.death)
#merge.data$days.death <- ifelse(merge.data$days.death >= 30,30,merge.data$days.death)
merge.data$day28.dead <- ifelse(merge.data$days.death>=28 |is.na(merge.data$days.death) ,0,1)


colnames(merge.data)[colnames(merge.data) == "days.death"] <- "time"
colnames(merge.data)[colnames(merge.data) == "day28.dead"] <- "status"


#colnames(merge.data)
#merge.data <-merge.data[,c(3,4,6:7,10:60)]
colnames(merge.data);dim(merge.data) # 2406  81



# 创建一个新的变量存储组别信息
merge.data$ethnicity_group <- NA

# 将种族分为四个组别：白人、黑人、黄种人、其他
merge.data$ethnicity_group[grepl("WHITE", merge.data$admissions.ethnicity, ignore.case = TRUE)] <- "White"
merge.data$ethnicity_group[grepl("BLACK", merge.data$admissions.ethnicity, ignore.case = TRUE)] <- "Black"
merge.data$ethnicity_group[grepl("ASIAN", merge.data$admissions.ethnicity, ignore.case = TRUE)] <- "Yellow"
merge.data$ethnicity_group[grepl("HISPANIC|PORTUGUESE", merge.data$admissions.ethnicity, ignore.case = TRUE)] <- "White"
merge.data$ethnicity_group[!(merge.data$ethnicity_group %in% c("White", "Black", "Yellow"))] <- "ZOther"

table(merge.data$ethnicity_group)

# 创建一个新的变量存储婚姻状态组别信息
merge.data$marital_status_group <- NA

# 将婚姻状态归类为四类
merge.data$marital_status_group[grepl("MARRIED", merge.data$admissions.marital_status, ignore.case = TRUE)] <- "MARRIED"
merge.data$marital_status_group[grepl("DIVORCED|SEPARATED|WIDOWED", merge.data$admissions.marital_status, ignore.case = TRUE)] <- "DIVORCED/SEPARATED/WIDOWED"
merge.data$marital_status_group[grepl("SINGLE", merge.data$admissions.marital_status, ignore.case = TRUE)] <- "SINGLE"
merge.data$marital_status_group[!(merge.data$marital_status_group %in% c("MARRIED", "DIVORCED/SEPARATED/WIDOWED", "SINGLE"))] <- "ZOther"

# 检查结果
table(merge.data$marital_status_group)


#### exclusion start ######
### 1. orignial icustay_id : 3376
table(merge.data$icustay_detail.admission_age)
# handle ange over 89
merge.data$icustay_detail.admission_age[merge.data$icustay_detail.admission_age > 89] <- 90

merge.data <- merge.data[merge.data$icustay_detail.admission_age > 18,]

dim(merge.data) # 2400     83
### 2. age > 18     --------->excluded 6

### exclude patients less than 24 hours
d_less_24 <- merge.data[merge.data$icustay_detail.los_icu <1,]; dim(d_less_24);
merge.data <- merge.data[!(merge.data$icustay_id %in% d_less_24$icustay_id),]
dim(merge.data) #2164   
### 3. los.icu > 1  --------->excluded 236

### BMI
#merge.data$BMI = merge.data$first_day_weight.weight/(merge.data$height.height*merge.data$height.height)

#### end exclusion ######


save(merge.data,file = "merge.data.Rdata")
#install.packages("mice")
#library(mice)


#### start handling missing data with mice package ####
rm(list = ls())
load(file = "merge.data.Rdata") # merge.data[,c(3:4,7:8,17:119,122:127)]

aggr_plot <- aggr(merge.data, col=c('navyblue','red'),
                  numbers=TRUE,
                  sortVars=TRUE,
                  labels=names(data),
                  cex.axis=.7,
                  gap=3,
                  ylab=c("数据缺失模式直方图","模式"))

## delete columns with data lost more than 20%
missings <- aggr_plot$missings
missings$percentage <- missings$Count/dim(merge.data)[1]
vars_to_drop <- missings$Variable[missings$percentage > 0.2]
merge.data<- merge.data[,-which(names(merge.data)%in%vars_to_drop)]
colnames(merge.data);dim(merge.data)
## check again
aggr_plot <- aggr(merge.data, col=c('navyblue','red'),
                  numbers=TRUE,
                  sortVars=TRUE,
                  labels=names(data),
                  cex.axis=.7,
                  gap=3,
                  ylab=c("数据缺失模式直方图","模式"))



missingVariables <- aggr_plot$missings$Variable[which(aggr_plot$missings$Count>0)]
noNAVariables <- aggr_plot$missings$Variable[which(aggr_plot$missings$Count==0)]


mice.data <- mice( merge.data[,missingVariables],method = "pmm",maxit = 5,seed = 3)
micedData <- complete(mice.data,3)

###=====
raw.ready <- cbind(merge.data[,noNAVariables],micedData)
colnames(raw.ready)

anyNA(raw.ready)
dim(raw.ready) # 313  80

table(merge.data$admissions.diagnosis)

raw.ready <- raw.ready[,c(3:6,13,54:79,19:53)]

# 将不需要的列存在下面
#nodeed_colume <- c("subject_id","hadm_id",)

names(raw.ready)
anyNA(raw.ready)
dim(raw.ready)  # 2164   66


save(raw.ready,file = "ready.data.Rdata")

