# 医学数据分析系统 - 结果展示界面
# Medical Data Analysis System - Results Display UI

# 统计图表界面
ui_plots <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("chart-bar", style = "margin-right: 15px;"),
          "统计图表",
          style = "margin: 0; font-weight: 300;"
        ),
        p("查看分析结果的可视化图表", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "图表选择",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          fluidRow(
            column(3,
              selectInput(
                "plot_type",
                "图表类型",
                choices = list(
                  "森林图" = "forest",
                  "ROC曲线" = "roc",
                  "校准曲线" = "calibration",
                  "决策曲线" = "dca",
                  "LASSO路径" = "lasso_path",
                  "相关性热图" = "correlation"
                ),
                selected = "forest"
              )
            ),
            
            column(3,
              selectInput(
                "plot_analysis",
                "分析结果",
                choices = list(
                  "单因素分析" = "univariate",
                  "多因素分析" = "multivariate",
                  "LASSO分析" = "lasso"
                ),
                selected = "univariate"
              )
            ),
            
            column(3,
              numericInput(
                "plot_width",
                "图表宽度",
                value = 800,
                min = 400,
                max = 1200,
                step = 50
              )
            ),
            
            column(3,
              numericInput(
                "plot_height",
                "图表高度",
                value = 600,
                min = 300,
                max = 1000,
                step = 50
              )
            )
          ),
          
          div(
            style = "text-align: center; margin-top: 20px;",
            actionButton(
              "generate_plot",
              "生成图表",
              icon = icon("chart-line"),
              class = "btn-primary"
            ),
            
            downloadButton(
              "download_plot",
              "下载图表",
              icon = icon("download"),
              class = "btn-success",
              style = "margin-left: 10px;"
            )
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "图表展示",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px; text-align: center;",
          
          div(
            id = "plot_placeholder",
            style = "padding: 100px; color: #6c757d;",
            icon("image", style = "font-size: 64px; margin-bottom: 20px; opacity: 0.5;"),
            h3("请选择图表类型并点击生成图表", style = "margin: 0; font-weight: 300;")
          ),
          
          div(
            id = "plot_container",
            style = "display: none;",
            plotOutput("analysis_plot", height = "auto")
          )
        )
      )
    )
  )
)

# 模型评估界面
ui_evaluation <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("tasks", style = "margin-right: 15px;"),
          "模型评估",
          style = "margin: 0; font-weight: 300;"
        ),
        p("评估预测模型的性能指标", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  # 模型性能指标
  fluidRow(
    column(3,
      valueBoxOutput("model_auc", width = NULL)
    ),
    column(3,
      valueBoxOutput("model_sensitivity", width = NULL)
    ),
    column(3,
      valueBoxOutput("model_specificity", width = NULL)
    ),
    column(3,
      valueBoxOutput("model_accuracy", width = NULL)
    )
  ),
  
  fluidRow(
    # 性能指标详情
    column(6,
      box(
        title = "性能指标详情",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          DT::dataTableOutput("model_metrics_table")
        )
      )
    ),
    
    # 混淆矩阵
    column(6,
      box(
        title = "混淆矩阵",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          plotOutput("confusion_matrix_plot", height = "300px")
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "模型比较",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "ROC比较",
              div(
                style = "margin-top: 15px;",
                plotOutput("roc_comparison_plot", height = "500px")
              )
            ),
            
            tabPanel(
              "校准比较",
              div(
                style = "margin-top: 15px;",
                plotOutput("calibration_comparison_plot", height = "500px")
              )
            ),
            
            tabPanel(
              "决策曲线比较",
              div(
                style = "margin-top: 15px;",
                plotOutput("dca_comparison_plot", height = "500px")
              )
            )
          )
        )
      )
    )
  )
)

# 交互式图表界面
ui_interactive <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("mouse-pointer", style = "margin-right: 15px;"),
          "交互式图表",
          style = "margin: 0; font-weight: 300;"
        ),
        p("可交互的动态图表和数据探索", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(4,
      box(
        title = "图表控制",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "interactive_plot_type",
            "图表类型",
            choices = list(
              "散点图" = "scatter",
              "箱线图" = "boxplot",
              "直方图" = "histogram",
              "密度图" = "density",
              "小提琴图" = "violin"
            ),
            selected = "scatter"
          ),
          
          conditionalPanel(
            condition = "input.interactive_plot_type == 'scatter'",
            selectInput("scatter_x", "X轴变量", choices = NULL),
            selectInput("scatter_y", "Y轴变量", choices = NULL),
            selectInput("scatter_color", "颜色分组", choices = NULL)
          ),
          
          conditionalPanel(
            condition = "input.interactive_plot_type != 'scatter'",
            selectInput("plot_variable", "分析变量", choices = NULL),
            selectInput("plot_group", "分组变量", choices = NULL)
          ),
          
          hr(),
          
          h5("图表选项"),
          checkboxInput("show_trend_line", "显示趋势线", value = FALSE),
          checkboxInput("show_confidence_interval", "显示置信区间", value = FALSE),
          
          sliderInput(
            "plot_alpha",
            "透明度",
            min = 0.1,
            max = 1,
            value = 0.7,
            step = 0.1
          ),
          
          hr(),
          
          actionButton(
            "update_interactive_plot",
            "更新图表",
            icon = icon("refresh"),
            class = "btn-primary",
            style = "width: 100%;"
          )
        )
      )
    ),
    
    column(8,
      box(
        title = "交互式图表",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          plotlyOutput("interactive_plot", height = "600px")
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "数据探索",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "变量关系",
              div(
                style = "margin-top: 15px;",
                plotlyOutput("correlation_interactive", height = "500px")
              )
            ),
            
            tabPanel(
              "分布探索",
              div(
                style = "margin-top: 15px;",
                plotlyOutput("distribution_interactive", height = "500px")
              )
            ),
            
            tabPanel(
              "缺失值模式",
              div(
                style = "margin-top: 15px;",
                plotlyOutput("missing_interactive", height = "500px")
              )
            )
          )
        )
      )
    )
  )
)
