# 医学数据分析系统 - 依赖包安装脚本
# Medical Data Analysis System - Package Installation Script

cat("开始安装医学数据分析系统所需的R包...\n")
cat("Starting installation of required R packages for Medical Data Analysis System...\n\n")

# 设置CRAN镜像
local({
  r <- getOption("repos")
  r["CRAN"] <- "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"
  options(repos = r)
  Bioc <- getOption("Bioc_mirror")
  Bioc["Bioc_mirror"] <- "https://mirrors.ustc.edu.cn/bioc/"
  options(Bioc_mirror = Bioc)
})

# 核心Shiny相关包
shiny_packages <- c(
  "shiny",           # Shiny框架
  "shinydashboard",  # Dashboard界面
  "shinyWidgets",    # 增强UI组件
  "DT",              # 数据表格
  "plotly",          # 交互式图表
  "shinycssloaders", # 加载动画
  "shinyjs",         # JavaScript集成
  "shinythemes",     # 主题
  "shinyFiles",      # 文件操作
  "shinyalert"       # 弹窗提示
)

# 数据处理包
data_packages <- c(
  "dplyr",           # 数据操作
  "tidyr",           # 数据整理
  "stringr",         # 字符串处理
  "lubridate",       # 日期时间处理
  "readr",           # 数据读取
  "readxl",          # Excel文件读取
  "mice",            # 缺失值处理
  "VIM",             # 缺失值可视化
  "janitor",         # 数据清洗
  "skimr"            # 数据概览
)

# 统计分析包
stats_packages <- c(
  "survival",        # 生存分析
  "survminer",       # 生存分析可视化
  "rms",             # 回归建模
  "glmnet",          # LASSO回归
  "caret",           # 机器学习
  "pROC",            # ROC分析
  "Hmisc",           # 统计函数
  "broom",           # 模型结果整理
  "tableone",        # 基线表格
  "table1",          # 描述性统计表
  "forestplot",      # 森林图
  "rmda",            # 决策曲线分析
  "nricens",         # NRI/IDI分析
  "PredictABEL",     # 预测模型评估
  "ResourceSelection" # 模型选择
)

# 可视化包
viz_packages <- c(
  "ggplot2",         # 基础绘图
  "ggpubr",          # 发表级图表
  "ggsci",           # 科学期刊配色
  "ggthemes",        # 图表主题
  "corrplot",        # 相关性图
  "pheatmap",        # 热图
  "RColorBrewer",    # 颜色方案
  "viridis",         # 颜色方案
  "gridExtra",       # 图表布局
  "cowplot"          # 图表组合
)

# 报告生成包
report_packages <- c(
  "rmarkdown",       # R Markdown
  "knitr",           # 动态报告
  "kableExtra",      # 表格美化
  "officer",         # Word文档
  "flextable",       # 灵活表格
  "webshot",         # 网页截图
  "htmlwidgets"      # HTML组件
)

# 工具包
utility_packages <- c(
  "config",          # 配置管理
  "logger",          # 日志记录
  "here",            # 路径管理
  "fs",              # 文件系统
  "jsonlite",        # JSON处理
  "yaml",            # YAML处理
  "devtools",        # 开发工具
  "testthat",        # 单元测试
  "profvis",         # 性能分析
  "future",          # 异步处理
  "promises"         # 异步编程
)

# 合并所有包
all_packages <- c(
  shiny_packages,
  data_packages, 
  stats_packages,
  viz_packages,
  report_packages,
  utility_packages
)

# 安装函数
install_if_missing <- function(packages) {
  for (pkg in packages) {
    if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
      cat(paste("安装包:", pkg, "\n"))
      cat(paste("Installing package:", pkg, "\n"))
      
      tryCatch({
        install.packages(pkg, dependencies = TRUE)
        cat(paste("✓ 成功安装:", pkg, "\n"))
        cat(paste("✓ Successfully installed:", pkg, "\n\n"))
      }, error = function(e) {
        cat(paste("✗ 安装失败:", pkg, "\n"))
        cat(paste("✗ Failed to install:", pkg, "\n"))
        cat(paste("错误信息:", e$message, "\n\n"))
        cat(paste("Error message:", e$message, "\n\n"))
      })
    } else {
      cat(paste("✓ 已安装:", pkg, "\n"))
      cat(paste("✓ Already installed:", pkg, "\n"))
    }
  }
}

# 执行安装
cat("开始安装依赖包...\n")
cat("Starting package installation...\n\n")

install_if_missing(all_packages)

# 验证安装
cat("\n验证安装结果...\n")
cat("\nVerifying installation...\n")

missing_packages <- c()
for (pkg in all_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    missing_packages <- c(missing_packages, pkg)
  }
}

if (length(missing_packages) == 0) {
  cat("\n🎉 所有依赖包安装成功！\n")
  cat("🎉 All packages installed successfully!\n")
  cat("\n现在可以运行以下命令启动应用：\n")
  cat("Now you can run the following command to start the app:\n")
  cat("shiny::runApp('medical_analysis_system')\n")
} else {
  cat("\n⚠️  以下包安装失败，请手动安装：\n")
  cat("⚠️  The following packages failed to install, please install manually:\n")
  cat(paste(missing_packages, collapse = ", "), "\n")
}

cat("\n安装完成！\n")
cat("Installation completed!\n")
