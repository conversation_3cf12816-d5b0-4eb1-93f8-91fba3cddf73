# 医学数据分析系统 - 主界面
# Medical Data Analysis System - Main UI

# 加载子界面模块
source("ui/ui_dashboard.R")
source("ui/ui_data.R")
source("ui/ui_analysis.R")
source("ui/ui_results.R")
source("ui/ui_reports.R")
source("ui/ui_settings.R")

# 定义主界面
ui <- dashboardPage(
  skin = "blue",
  title = "医学数据分析系统-Medical Data Analysis System",
  
  # 页面头部
  dashboardHeader(
    title = tags$span(
      tags$i(class = "fa fa-heartbeat", style = "color: #e74c3c;"),
      " 医学数据分析系统",
      style = "font-size: 18px; font-weight: bold;"
    ),
    titleWidth = 280,
    
    # 头部菜单
    dropdownMenu(
      type = "notifications",
      icon = icon("bell"),
      badgeStatus = "info",
      headerText = "系统通知",
      notificationItem(
        text = "系统运行正常",
        icon = icon("check"),
        status = "success"
      )
    ),
    
    dropdownMenu(
      type = "tasks",
      icon = icon("tasks"),
      badgeStatus = "primary",
      headerText = "分析任务",
      taskItem(
        value = 0,
        text = "暂无运行中的任务",
        color = "green"
      )
    )
  ),
  
  # 侧边栏
  dashboardSidebar(
    width = 280,
    
    
    
    # 侧边栏菜单
    sidebarMenu(
      id = "sidebar_menu",
      
      menuItem(
        "仪表板",
        tabName = "dashboard",
        icon = icon("tachometer-alt"),
        badgeLabel = "主页",
        badgeColor = "blue"
      ),
      
      menuItem(
        "数据管理",
        tabName = "data",
        icon = icon("database"),
        menuSubItem("单文件上传", tabName = "data_upload"),
        menuSubItem("多文件合并", tabName = "multi_file_upload"),
        menuSubItem("数据预览", tabName = "data_preview"),
        menuSubItem("数据清洗", tabName = "data_cleaning")
      ),
      
      menuItem(
        "统计分析",
        tabName = "analysis",
        icon = icon("chart-line"),
        menuSubItem("描述性统计", tabName = "descriptive"),
        menuSubItem("单因素分析", tabName = "univariate"),
        menuSubItem("多因素分析", tabName = "multivariate"),
        menuSubItem("LASSO回归", tabName = "lasso")
      ),
      
      menuItem(
        "模型构建",
        tabName = "modeling",
        icon = icon("cogs"),
        menuSubItem("Logistic回归", tabName = "logistic"),
        menuSubItem("生存分析", tabName = "survival"),
        menuSubItem("列线图", tabName = "nomogram")
      ),
      
      menuItem(
        "结果展示",
        tabName = "results",
        icon = icon("chart-bar"),
        menuSubItem("统计图表", tabName = "plots"),
        menuSubItem("模型评估", tabName = "evaluation"),
        menuSubItem("交互式图表", tabName = "interactive")
      ),
      
      menuItem(
        "报告中心",
        tabName = "reports",
        icon = icon("file-alt"),
        menuSubItem("生成报告", tabName = "generate_report"),
        menuSubItem("报告历史", tabName = "report_history"),
        menuSubItem("模板管理", tabName = "templates")
      ),
      
      menuItem(
        "系统设置",
        tabName = "settings",
        icon = icon("cog"),
        menuSubItem("参数配置", tabName = "config"),
        menuSubItem("用户偏好", tabName = "preferences"),
        menuSubItem("关于系统", tabName = "about")
      )
    ),
    
    # 侧边栏底部信息
    div(
      style = "position: absolute; bottom: 10px; left: 15px; right: 15px; 
               color: #aaa; font-size: 11px; text-align: center;",
      div("版本 1.0.0"),
      div("© 2024 Medical Analysis Team")
    )
  ),
  
  # 主体内容
  dashboardBody(
    # 使用shinyjs
    useShinyjs(),
    
    # 自定义CSS和JavaScript
    tags$head(
      # 设置页面标题（浏览器标签页显示）
      tags$title("医学数据分析系统"),
      
      tags$link(rel = "stylesheet", type = "text/css", href = "css/custom.css"),
      tags$script(src = "js/custom.js"),

      # 添加文件输入重置功能
      tags$script(HTML("
        Shiny.addCustomMessageHandler('resetFileInput', function(inputId) {
          var input = document.getElementById(inputId);
          if (input) {
            input.value = '';
            // 触发change事件以通知Shiny
            var event = new Event('change', { bubbles: true });
            input.dispatchEvent(event);
          }
        });
      "))
    ),
    
    # 页面内容
    tabItems(
      # 仪表板页面
      tabItem(
        tabName = "dashboard",
        ui_dashboard
      ),
      
      # 数据管理页面
      tabItem(tabName = "data_upload", ui_data_upload),
      tabItem(tabName = "multi_file_upload", ui_multi_file_upload),
      tabItem(tabName = "data_preview", ui_data_preview),
      tabItem(tabName = "data_cleaning", ui_data_cleaning),
      
      # 统计分析页面
      tabItem(tabName = "descriptive", ui_descriptive),
      tabItem(tabName = "univariate", ui_univariate),
      tabItem(tabName = "multivariate", ui_multivariate),
      tabItem(tabName = "lasso", ui_lasso),
      
      # 模型构建页面
      tabItem(tabName = "logistic", ui_logistic),
      tabItem(tabName = "survival", ui_survival),
      tabItem(tabName = "nomogram", ui_nomogram),
      
      # 结果展示页面
      tabItem(tabName = "plots", ui_plots),
      tabItem(tabName = "evaluation", ui_evaluation),
      tabItem(tabName = "interactive", ui_interactive),
      
      # 报告中心页面
      tabItem(tabName = "generate_report", ui_generate_report),
      tabItem(tabName = "report_history", ui_report_history),
      tabItem(tabName = "templates", ui_templates),
      
      # 系统设置页面
      tabItem(tabName = "config", ui_config),
      tabItem(tabName = "preferences", ui_preferences),
      tabItem(tabName = "about", ui_about)
    )
  )
)
