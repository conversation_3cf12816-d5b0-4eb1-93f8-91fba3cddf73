# 医学数据分析系统 - 辅助函数
# Medical Data Analysis System - Helper Functions

# 生成示例数据
generate_sample_data <- function(n = 1000, seed = 123) {
  set.seed(seed)
  
  # 基本人口学变量
  age <- round(rnorm(n, 65, 15))
  age[age < 18] <- 18
  age[age > 100] <- 100
  
  gender <- sample(c("Male", "Female"), n, replace = TRUE, prob = c(0.55, 0.45))
  
  # 生化指标
  creatinine <- round(rnorm(n, 1.2, 0.4), 2)
  creatinine[creatinine < 0.5] <- 0.5
  creatinine[creatinine > 5.0] <- 5.0
  
  bun <- round(rnorm(n, 20, 8), 1)
  bun[bun < 5] <- 5
  bun[bun > 80] <- 80
  
  wbc <- round(rnorm(n, 8.5, 3.2), 1)
  wbc[wbc < 2] <- 2
  wbc[wbc > 25] <- 25
  
  hemoglobin <- round(rnorm(n, 12, 2.5), 1)
  hemoglobin[hemoglobin < 6] <- 6
  hemoglobin[hemoglobin > 18] <- 18
  
  # 生命体征
  heart_rate <- round(rnorm(n, 85, 20))
  heart_rate[heart_rate < 40] <- 40
  heart_rate[heart_rate > 180] <- 180
  
  systolic_bp <- round(rnorm(n, 130, 25))
  systolic_bp[systolic_bp < 80] <- 80
  systolic_bp[systolic_bp > 220] <- 220
  
  respiratory_rate <- round(rnorm(n, 18, 5))
  respiratory_rate[respiratory_rate < 8] <- 8
  respiratory_rate[respiratory_rate > 40] <- 40
  
  # 评分系统
  sofa_score <- round(rnorm(n, 6, 3))
  sofa_score[sofa_score < 0] <- 0
  sofa_score[sofa_score > 20] <- 20
  
  apache_score <- round(rnorm(n, 15, 8))
  apache_score[apache_score < 0] <- 0
  apache_score[apache_score > 50] <- 50
  
  # 合并症
  diabetes <- sample(c(0, 1), n, replace = TRUE, prob = c(0.7, 0.3))
  hypertension <- sample(c(0, 1), n, replace = TRUE, prob = c(0.6, 0.4))
  heart_disease <- sample(c(0, 1), n, replace = TRUE, prob = c(0.75, 0.25))
  kidney_disease <- sample(c(0, 1), n, replace = TRUE, prob = c(0.8, 0.2))
  
  # 住院时长
  los_icu <- round(rexp(n, 0.2))
  los_icu[los_icu < 1] <- 1
  los_icu[los_icu > 30] <- 30
  
  # 结局变量（28天死亡率）
  # 基于多个因素计算死亡概率
  death_prob <- plogis(-2.5 + 
                      0.05 * (age - 65) + 
                      0.3 * (gender == "Male") +
                      0.2 * sofa_score +
                      0.1 * apache_score +
                      0.5 * diabetes +
                      0.3 * heart_disease +
                      0.4 * kidney_disease +
                      0.1 * (creatinine - 1.2) +
                      0.02 * (wbc - 8.5))
  
  death_28d <- rbinom(n, 1, death_prob)
  
  # 添加一些缺失值
  missing_indices <- sample(1:n, n * 0.05)  # 5%缺失率
  creatinine[sample(missing_indices, length(missing_indices) * 0.3)] <- NA
  bun[sample(missing_indices, length(missing_indices) * 0.2)] <- NA
  hemoglobin[sample(missing_indices, length(missing_indices) * 0.1)] <- NA
  
  # 创建数据框
  sample_data <- data.frame(
    patient_id = 1:n,
    age = age,
    gender = gender,
    creatinine = creatinine,
    bun = bun,
    wbc = wbc,
    hemoglobin = hemoglobin,
    heart_rate = heart_rate,
    systolic_bp = systolic_bp,
    respiratory_rate = respiratory_rate,
    sofa_score = sofa_score,
    apache_score = apache_score,
    diabetes = diabetes,
    hypertension = hypertension,
    heart_disease = heart_disease,
    kidney_disease = kidney_disease,
    los_icu = los_icu,
    death_28d = death_28d,
    stringsAsFactors = FALSE
  )
  
  return(sample_data)
}

# 数据类型检测和转换
detect_and_convert_types <- function(data) {
  tryCatch({
    converted_data <- data
    
    for (col in names(data)) {
      col_data <- data[[col]]
      
      # 跳过完全缺失的列
      if (all(is.na(col_data))) next
      
      # 移除缺失值进行判断
      non_na_data <- col_data[!is.na(col_data)]
      
      # 检查是否为数值型
      if (is.character(col_data)) {
        # 尝试转换为数值
        numeric_test <- suppressWarnings(as.numeric(non_na_data))
        if (!all(is.na(numeric_test))) {
          # 如果能成功转换为数值
          converted_data[[col]] <- as.numeric(col_data)
        } else {
          # 检查是否为分类变量
          unique_values <- length(unique(non_na_data))
          if (unique_values <= 20) {
            converted_data[[col]] <- as.factor(col_data)
          }
        }
      } else if (is.numeric(col_data)) {
        # 检查是否应该转换为因子
        unique_values <- length(unique(non_na_data))
        if (unique_values <= 10 && all(non_na_data == round(non_na_data))) {
          # 如果唯一值少且都是整数，可能是分类变量
          if (unique_values <= 5) {
            converted_data[[col]] <- as.factor(col_data)
          }
        }
      }
    }
    
    log_info("数据类型检测和转换完成")
    return(converted_data)
    
  }, error = function(e) {
    log_error(paste("数据类型转换失败:", e$message))
    return(data)
  })
}

# 获取变量选择列表
get_variable_choices <- function(data, exclude_vars = NULL) {
  if (is.null(data)) return(NULL)
  
  var_names <- names(data)
  if (!is.null(exclude_vars)) {
    var_names <- var_names[!var_names %in% exclude_vars]
  }
  
  # 创建带有变量类型信息的选择列表
  choices <- list()
  for (var in var_names) {
    var_type <- class(data[[var]])[1]
    if (var_type == "numeric") {
      type_label <- "数值"
    } else if (var_type == "factor") {
      type_label <- "分类"
    } else if (var_type == "character") {
      type_label <- "字符"
    } else if (var_type == "logical") {
      type_label <- "逻辑"
    } else {
      type_label <- "其他"
    }
    
    choices[[paste0(var, " (", type_label, ")")]] <- var
  }
  
  return(choices)
}

# 创建变量信息表
create_variable_info_table <- function(data) {
  if (is.null(data) || nrow(data) == 0) return(NULL)
  
  var_info <- data.frame(
    Variable = names(data),
    Type = sapply(data, function(x) class(x)[1]),
    Missing = sapply(data, function(x) sum(is.na(x))),
    Missing_Percent = round(sapply(data, function(x) sum(is.na(x)) / length(x) * 100), 2),
    Unique_Values = sapply(data, function(x) length(unique(x[!is.na(x)]))),
    stringsAsFactors = FALSE
  )
  
  # 添加描述性统计
  var_info$Description <- ""
  for (i in 1:nrow(var_info)) {
    var_name <- var_info$Variable[i]
    var_data <- data[[var_name]]
    
    if (is.numeric(var_data)) {
      mean_val <- round(mean(var_data, na.rm = TRUE), 2)
      sd_val <- round(sd(var_data, na.rm = TRUE), 2)
      var_info$Description[i] <- paste0("Mean: ", mean_val, ", SD: ", sd_val)
    } else if (is.factor(var_data) || is.character(var_data)) {
      top_level <- names(sort(table(var_data), decreasing = TRUE))[1]
      var_info$Description[i] <- paste0("Most frequent: ", top_level)
    }
  }
  
  return(var_info)
}

# 格式化统计结果表格
format_results_table <- function(results_df) {
  if (is.null(results_df) || nrow(results_df) == 0) return(NULL)
  
  formatted_df <- results_df
  
  # 格式化数值列
  numeric_cols <- sapply(formatted_df, is.numeric)
  for (col in names(formatted_df)[numeric_cols]) {
    if (col %in% c("p_value", "p.value")) {
      formatted_df[[col]] <- format_pvalue(formatted_df[[col]])
    } else if (col %in% c("OR", "HR", "RR")) {
      formatted_df[[col]] <- round(formatted_df[[col]], 3)
    } else {
      formatted_df[[col]] <- round(formatted_df[[col]], 4)
    }
  }
  
  return(formatted_df)
}

# 创建下载处理器
create_download_handler <- function(data, filename_prefix = "analysis_results") {
  downloadHandler(
    filename = function() {
      paste0(filename_prefix, "_", Sys.Date(), ".csv")
    },
    content = function(file) {
      write.csv(data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
}

# 验证分析参数
validate_analysis_params <- function(data, outcome_var, covariates = NULL) {
  errors <- c()
  
  # 检查数据
  if (is.null(data) || nrow(data) == 0) {
    errors <- c(errors, "数据为空")
  }
  
  # 检查结局变量
  if (is.null(outcome_var) || outcome_var == "") {
    errors <- c(errors, "未选择结局变量")
  } else if (!outcome_var %in% names(data)) {
    errors <- c(errors, "结局变量不存在于数据中")
  } else {
    # 检查结局变量是否为二分类
    unique_values <- unique(data[[outcome_var]][!is.na(data[[outcome_var]])])
    if (length(unique_values) != 2) {
      errors <- c(errors, "结局变量必须是二分类变量")
    }
  }
  
  # 检查协变量
  if (!is.null(covariates) && length(covariates) > 0) {
    missing_covariates <- covariates[!covariates %in% names(data)]
    if (length(missing_covariates) > 0) {
      errors <- c(errors, paste("以下协变量不存在于数据中:", 
                               paste(missing_covariates, collapse = ", ")))
    }
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors
  ))
}

# 创建进度更新函数
create_progress_updater <- function(session, total_steps) {
  current_step <- 0
  
  function(message = "", increment = TRUE) {
    if (increment) {
      current_step <<- current_step + 1
    }
    
    progress_value <- current_step / total_steps * 100
    
    session$sendCustomMessage(
      type = "updateProgress",
      message = list(
        value = progress_value,
        text = message
      )
    )
  }
}

# 安全执行函数（带错误处理）
safe_execute <- function(expr, error_message = "操作失败", 
                        success_message = NULL, show_notifications = TRUE) {
  tryCatch({
    result <- expr
    
    if (!is.null(success_message) && show_notifications) {
      show_success(success_message)
    }
    
    return(result)
    
  }, error = function(e) {
    error_msg <- paste(error_message, ":", e$message)
    
    if (show_notifications) {
      show_warning(error_msg)
    }
    
    log_error(error_msg)
    return(NULL)
  })
}

# 创建响应式数据验证器
create_data_validator <- function(data_reactive) {
  reactive({
    data <- data_reactive()
    
    if (is.null(data)) {
      return(list(valid = FALSE, message = "请先上传数据"))
    }
    
    if (nrow(data) == 0) {
      return(list(valid = FALSE, message = "数据为空"))
    }
    
    if (ncol(data) < 2) {
      return(list(valid = FALSE, message = "数据列数不足"))
    }
    
    return(list(valid = TRUE, message = "数据验证通过"))
  })
}

# 格式化文件大小
format_file_size <- function(size_bytes) {
  if (size_bytes < 1024) {
    return(paste(size_bytes, "B"))
  } else if (size_bytes < 1024^2) {
    return(paste(round(size_bytes / 1024, 1), "KB"))
  } else if (size_bytes < 1024^3) {
    return(paste(round(size_bytes / 1024^2, 1), "MB"))
  } else {
    return(paste(round(size_bytes / 1024^3, 1), "GB"))
  }
}
