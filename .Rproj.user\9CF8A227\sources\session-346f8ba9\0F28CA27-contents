library(compareGroups)  # 加载包

# 将相关变量因子化
newdata <- merge.data
colnames(newdata)
for(i in c(5:7,35:64,66)){
  newdata[[i]] <- as.factor(newdata[[i]])
}
#newdata[,max_col_num] <- as.factor(newdata[,max_col_num])

#str(merge.data) # 查看数据集结构
#descrTable( ~ ., data = merge.data)

#选择分组变量
restab <- descrTable( anaGroup ~ . 
                     ,data = newdata
                     ,show.all = TRUE
                     ,method=4
                     )


## export 

export2xls(restab, file = paste0(filepathDate,"/base line.xlsx"))

rm(newdata)
