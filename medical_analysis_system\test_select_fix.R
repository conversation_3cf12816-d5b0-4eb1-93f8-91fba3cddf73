# 测试全选/反选功能修复
# Test Select All/None Functionality Fix

cat("测试全选/反选功能修复...\n")
cat("Testing select all/none functionality fix...\n\n")

# 设置工作目录
if (basename(getwd()) != "medical_analysis_system") {
  if (dir.exists("medical_analysis_system")) {
    setwd("medical_analysis_system")
  } else {
    stop("请在包含medical_analysis_system目录的位置运行此脚本")
  }
}

# 加载必要的模块
tryCatch({
  source("global.R")
  cat("✓ 全局配置加载成功\n")
}, error = function(e) {
  cat("✗ 全局配置加载失败:", e$message, "\n")
  stop("系统初始化失败")
})

# 测试get_variable_choices函数的返回格式
cat("\n=== 测试get_variable_choices函数 ===\n")

# 创建测试数据
test_data <- data.frame(
  patient_id = 1:10,
  age = c(25, 30, 35, 40, 45, 50, 55, 60, 65, 70),
  gender = c("M", "F", "M", "F", "M", "F", "M", "F", "M", "F"),
  creatinine = c(1.0, 1.2, 1.1, 1.3, 1.4, 1.2, 1.5, 1.1, 1.3, 1.2),
  death_28d = c(0, 1, 0, 1, 0, 1, 0, 1, 0, 1)
)

# 测试get_variable_choices函数
var_choices <- get_variable_choices(test_data)

cat("get_variable_choices返回结果:\n")
cat("类型:", class(var_choices), "\n")
cat("长度:", length(var_choices), "\n")
cat("结构:\n")
str(var_choices)

cat("\n选择列表内容:\n")
for (i in 1:length(var_choices)) {
  cat("  键:", names(var_choices)[i], "\n")
  cat("  值:", var_choices[[i]], "\n")
}

# 测试修复后的选择逻辑
cat("\n=== 测试修复后的选择逻辑 ===\n")

# 模拟原来的错误方法
old_method <- names(var_choices)
cat("原方法 names(var_choices):\n")
print(old_method)

# 模拟修复后的正确方法
new_method <- as.character(var_choices)
cat("\n新方法 as.character(var_choices):\n")
print(new_method)

# 验证新方法是否返回正确的变量名
expected_vars <- names(test_data)
cat("\n期望的变量名:\n")
print(expected_vars)

cat("\n验证结果:\n")
if (all(new_method %in% expected_vars)) {
  cat("✓ 新方法返回正确的变量名\n")
} else {
  cat("✗ 新方法返回的变量名不正确\n")
  cat("  缺失的变量:", setdiff(expected_vars, new_method), "\n")
  cat("  多余的变量:", setdiff(new_method, expected_vars), "\n")
}

# 检查服务器代码修复
cat("\n=== 检查服务器代码修复 ===\n")

server_content <- readLines("server/server_main.R", warn = FALSE)

# 检查是否使用了正确的方法
correct_patterns <- c(
  "as\\.character\\(values\\$desc_var_choices\\)",
  "as\\.character\\(values\\$analysis_var_choices\\)"
)

incorrect_patterns <- c(
  "names\\(values\\$desc_var_choices\\)",
  "names\\(values\\$analysis_var_choices\\)"
)

cat("检查正确的修复模式:\n")
for (pattern in correct_patterns) {
  matches <- sum(grepl(pattern, server_content))
  cat("  ", pattern, ":", matches, "次匹配\n")
}

cat("\n检查是否还有错误的模式:\n")
for (pattern in incorrect_patterns) {
  matches <- sum(grepl(pattern, server_content))
  cat("  ", pattern, ":", matches, "次匹配")
  if (matches > 0) {
    cat(" ⚠️ 需要修复")
  }
  cat("\n")
}

# 模拟updateCheckboxGroupInput的行为
cat("\n=== 模拟updateCheckboxGroupInput行为 ===\n")

# 模拟checkboxGroupInput的choices
checkbox_choices <- var_choices

cat("checkboxGroupInput的choices:\n")
print(checkbox_choices)

# 模拟全选操作
selected_values <- as.character(var_choices)
cat("\n全选时的selected值:\n")
print(selected_values)

# 验证selected值是否在choices中
valid_selections <- selected_values %in% as.character(checkbox_choices)
cat("\n选择验证结果:\n")
for (i in 1:length(selected_values)) {
  cat("  ", selected_values[i], ":", ifelse(valid_selections[i], "✓ 有效", "✗ 无效"), "\n")
}

if (all(valid_selections)) {
  cat("✓ 所有选择都有效，全选功能应该正常工作\n")
} else {
  cat("✗ 存在无效选择，全选功能可能不工作\n")
}

cat("\n=== 修复总结 ===\n")
cat("问题原因:\n")
cat("- get_variable_choices()返回命名列表，键是带类型的标签，值是变量名\n")
cat("- 原代码使用names()获取键（标签），而不是实际的变量名\n")
cat("- updateCheckboxGroupInput需要的是实际的变量名作为selected值\n\n")

cat("修复方案:\n")
cat("- 将names(values$desc_var_choices)改为as.character(values$desc_var_choices)\n")
cat("- 将names(values$analysis_var_choices)改为as.character(values$analysis_var_choices)\n")
cat("- 这样可以获取列表中的实际变量名而不是标签\n\n")

cat("修复状态:\n")
correct_fixes <- sum(grepl("as\\.character\\(values\\$.*_var_choices\\)", server_content))
cat("- 正确修复的地方:", correct_fixes, "\n")

remaining_errors <- sum(grepl("names\\(values\\$.*_var_choices\\)", server_content))
cat("- 仍需修复的地方:", remaining_errors, "\n")

if (remaining_errors == 0) {
  cat("✓ 全选/反选功能修复完成！\n")
} else {
  cat("⚠️ 仍有部分代码需要修复\n")
}

cat("\n全选/反选功能现在应该可以正常工作了！\n")
cat("Select all/none functionality should now work properly!\n")
