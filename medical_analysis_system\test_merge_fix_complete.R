# 完整的合并功能测试脚本
# Complete Merge Function Test Script

cat("=== 测试多文件合并完整修复 ===\n")

# 设置工作目录
if (basename(getwd()) != "medical_analysis_system") {
  if (dir.exists("medical_analysis_system")) {
    setwd("medical_analysis_system")
  }
}

# 加载必要的函数
tryCatch({
  source("utils/helpers.R")
  source("modules/multi_file_processing.R")
  cat("✓ 成功加载必要函数\n")
}, error = function(e) {
  cat("✗ 加载函数失败:", e$message, "\n")
  stop("无法继续测试")
})

# 创建测试数据
cat("\n创建测试数据...\n")

# 测试文件1
data1 <- data.frame(
  icustay_id = c(1001, 1002, 1003, 1004),
  age = c(65, 72, 58, 81),
  gender = c("M", "F", "M", "F"),
  stringsAsFactors = FALSE
)

# 测试文件2
data2 <- data.frame(
  icustay_id = c(1001, 1002, 1004, 1005),
  diagnosis = c("Pneumonia", "COPD", "Heart Failure", "Sepsis"),
  los_days = c(5, 8, 12, 15),
  stringsAsFactors = FALSE
)

# 测试文件3
data3 <- data.frame(
  icustay_id = c(1001, 1003, 1006),
  lab_value = c(12.5, 8.2, 15.7),
  unit = c("mg/dL", "mg/dL", "mg/dL"),
  stringsAsFactors = FALSE
)

# 创建文件列表
test_files <- list(
  file1 = list(
    name = "demographics.csv",
    description = "患者基本信息",
    data = data1
  ),
  file2 = list(
    name = "clinical.csv", 
    description = "临床信息",
    data = data2
  ),
  file3 = list(
    name = "laboratory.csv",
    description = "实验室检查",
    data = data3
  )
)

cat("✓ 测试数据创建完成\n")

# 测试1: 正常合并
cat("\n=== 测试1: 正常合并 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = "icustay_id",
    merge_type = "inner",
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ 正常合并成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  cat("合并结果:\n")
  print(result$data)
  
}, error = function(e) {
  cat("✗ 正常合并失败:", e$message, "\n")
})

# 测试2: 空merge_key参数
cat("\n=== 测试2: 空merge_key参数 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = "",  # 空字符串
    merge_type = "inner",
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ 空merge_key参数处理成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  
}, error = function(e) {
  cat("✗ 空merge_key参数处理失败:", e$message, "\n")
})

# 测试3: NULL merge_key参数
cat("\n=== 测试3: NULL merge_key参数 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = NULL,  # NULL值
    merge_type = "inner",
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ NULL merge_key参数处理成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  
}, error = function(e) {
  cat("✗ NULL merge_key参数处理失败:", e$message, "\n")
})

# 测试4: 空向量merge_key参数
cat("\n=== 测试4: 空向量merge_key参数 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = character(0),  # 空向量
    merge_type = "inner",
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ 空向量merge_key参数处理成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  
}, error = function(e) {
  cat("✗ 空向量merge_key参数处理失败:", e$message, "\n")
})

# 测试5: 多值merge_key参数
cat("\n=== 测试5: 多值merge_key参数 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = c("icustay_id", "age"),  # 多值向量
    merge_type = "inner",
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ 多值merge_key参数处理成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  
}, error = function(e) {
  cat("✗ 多值merge_key参数处理失败:", e$message, "\n")
})

# 测试6: 无效merge_type参数
cat("\n=== 测试6: 无效merge_type参数 ===\n")

tryCatch({
  result <- merge_medical_files(
    file_list = test_files,
    merge_key = "icustay_id",
    merge_type = "invalid_type",  # 无效类型
    remove_duplicates = TRUE,
    add_source_info = FALSE
  )
  
  cat("✓ 无效merge_type参数处理成功！\n")
  cat("结果维度:", nrow(result$data), "行,", ncol(result$data), "列\n")
  
}, error = function(e) {
  cat("✗ 无效merge_type参数处理失败:", e$message, "\n")
})

cat("\n=== 所有测试完成 ===\n")
cat("如果所有测试都通过，说明修复成功！\n") 