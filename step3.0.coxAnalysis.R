rm(list = ls())
load(file = "ready.data.Rdata")
names(raw.ready)
merge.data <- raw.ready
dim(merge.data)
names(merge.data)

max_col_num = length(merge.data);max_col_num

# handling the age
#merge.data$icustay_detail.admission_age[merge.data$icustay_detail.admission_age > 89] <- 90

count <- 0

auc_all <- data.frame()

# 这里使用repeat来替代while，因为在while循环中没有对count进行更新，可能会导致死循环
repeat {
  # 尝试执行一个可能会出错的操作
  try({
    today <- format(Sys.time(), format="%Y-%m-%d-%H-%M-%S")
    
    if(!dir.exists('export')) {dir.create('export')}
    filepathDate <- paste0("export/", today)
    if(!dir.exists(filepathDate)) {dir.create(filepathDate)}
    
    seerd <- sample(nrow(merge.data), 0.7 * nrow(merge.data))
    cohort.dev <- merge.data[seerd,]
    cohort.val <- merge.data[-seerd,]
    merge.data$anaGroup <- ifelse(row.names(merge.data) %in% row.names(cohort.dev), "training cohort", "validation cohort")
    
    save(today, merge.data, file = paste0(filepathDate, "/merge.data.grouped.Rdata"))
    
    cohort.dev$anaGroup <- "training cohort"
    cohort.val$anaGroup <- "validation cohort"
    
    ## review for the existing model
    #filepathDate <-'export/2024-03-19-13-06-57'
    #merge.data$icustay_detail.admission_age[merge.data$icustay_detail.admission_age > 89] <- 90
    
    
    #cohort.dev <- merge.data[merge.data$anaGroup == 'training cohort',]
    #cohort.val <- merge.data[merge.data$anaGroup == 'validation cohort',]
    
    
    source('step3.1.compareGroups.R')
    
    dataInput <- cohort.dev
    filepath <- paste0(filepathDate, "/cohort_development")
    source('runLogistic_development.R')
    
    new_row <- data.frame('folder' = today, 'group' = 'development', 'auc' = auc1, 'c.index' = c_index$C.value)
    auc_all <- rbind(auc_all, new_row)
    
    dataInput <- cohort.val
    filepath <- paste0(filepathDate, "/cohort_validation")
    source('runLogistic_validation.R')
    
    new_row <- data.frame('folder' = today, 'group' = 'validation', 'auc' = auc1, 'c.index' = c_index$C.value)
    auc_all <- rbind(auc_all, new_row)
    
    cat("One cycle finished\n")
    
    # 在每一轮循环结束后记录日志
    writeLines(paste("Count:", count, "Time:", Sys.time()), "export/log.txt")
    
    
  }, silent = FALSE)
  
  Sys.sleep(1)
  print(count)
  count <- count + 1
  if (count >= 20) {
    break  # 跳出循环
  }
}

write.csv(auc_all, file = paste0('export/auc_all_', today, '.csv'), row.names = FALSE)







