
#setwd("~/Desktop/1_workFile/seer/1. R-Cox prediction model")
biocPackages <- c(
  "stringr",  
  "survminer", 
  "foreign",    
  "regplot",
  "rms",
  "survivalROC",
  "table1",                            
  "devtools",
  "rmda",
  "dplyr",
  "plyr",
  "mice",
  "VIM",
  "nricens"
)

local({
  r <- getOption("repos")
  r["CRAN"] <- "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"
  options(repos = r)
  Bioc <- getOption("Bioc_mirror")
  Bioc["Bioc_mirror"] <- "https://mirrors.ustc.edu.cn/bioc/"
  options(Bioc_mirror=Bioc)
})

## install packages
#source("https://bioconductor.org/biocLite.R")
lapply(biocPackages,
       function(biocPackage){
         if(!require(biocPackage, character.only = T)){
           CRANpackages <- BiocManager::available()
           if(biocPackage %in% rownames(CRANpackages)){
             install.packages(biocPackage)
           }else{
             BiocManager::install(biocPackage,ask = F,update = T)
           }
         }
       }
)


  # 载入R包
if (T) {
  library("stringr")
  library("survminer")
  library(foreign)
  library(regplot)
  library(rms)
  library(survivalROC)
  library(table1)
  library(caret)
  library(rmda)
  library(nricens)
  library(plyr)
  library(mice)
  library(VIM)
  library(dplyr)
  library(plyr)
  library(stringr)
  library(lubridate)
  library(table1)
  library(rjson)
  library(jsonlite)
  library(tableone)
  library(tidyverse)
  library(caret)
  library(leaps)
  library("glmnet")
  library(foreign)
  library(glmnet)
}

install.packages("broom")
install.packages("dplyr")
install.packages("ggplot2",update=T)
install.packages("lubridate",update=T)
install.packages("reprex",update=T)
install.packages("tidyverse",update=T)
install.packages("tidyr",update=T)
install.packages("glmnet")