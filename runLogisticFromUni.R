if(!dir.exists(filepath)){dir.create(filepath)}
covariates <- colnames(dataInput)[c(2:44,48)];covariates

uni_glim_model <-
  function(x){
    FML <- as.formula(paste0("status==1~",x))
    glm1<-glm(FML,data=dataInput,family= binomial)
    glm2<-summary(glm1)
    variables <- glm2$aliased[-1]
    OR <- round (exp(coef(glm1)),2)
    SE <- glm2$coefficients[,2]
    CI5 <- round(exp(coef(glm1)-1.96*SE),2)
    CI95 <- round(exp(coef(glm1)******SE),2)
    CI <-paste0(CI5,'-',CI95)
    p.value <- round(glm2$coefficients[,4],2)
    p.star <- ifelse(p.value<0.001,"***",ifelse(p.value<0.01,"**",ifelse(p.value<0.05,"*","")))
    uni_glim_model <- data.frame(
                                 'OR'= OR,
                                 'CI' = CI,
                                 'p' = p.value,
                                 'p.star'= p.star)[-1,]
    uni_glim_model$variables <- row.names(uni_glim_model)
    
    return(uni_glim_model[,c(5,1:4)])
  }

uni_glm <- lapply(covariates,uni_glim_model)
library(plyr)
uni_glm <-ldply(uni_glm,data.frame);uni_glm
write.csv(uni_glm,file = paste0(filepath,"/1.1 single_logistic_Summary.csv"),row.names = T)

### multiple variables
uni_glm[uni_glm$p<0.05,]
factorsInput <- "
age.first_admit_age+
sapsii.sapsii+
sofa.sofa+
apsiii.apsiii+
#oasis.gcs+
labsfirstday.aniongap_mean+
labsfirstday.platelet_mean+
labsfirstday.creatinine_mean+
labsfirstday.bun_mean+
labsfirstday.wbc_mean+
vitals_first_day.tempc_mean+
vitals_first_day.spo2_mean+
vitals_first_day.glucose_mean+
com_hypertension+
com_fluid_electrolyte+
marital"


mul_glm <- glm(as.formula(paste0("status==1~",factorsInput)), data = dataInput, family = binomial())
library(broom)
OR <- round(exp(coef(mul_glm)),2)
ci95 <- paste0(sprintf("%.2f", exp(confint(mul_glm))[,1]), "-",sprintf("%.2f", exp(confint(mul_glm))[,2]))
p.value <- round(tidy(mul_glm)$p.value,2)
p.star <- ifelse(p.value<0.001,"***",ifelse(p.value<0.01,"**",ifelse(p.value<0.05,"*","")))

mul_glim_model <- data.frame('OR'= OR,'CI' = ci95,'p' = p.value,'p.star'= p.star)[-1,]
write.csv(mul_glim_model,file = paste0(filepath,"/1.2 multi_logistic_Summary.csv"),row.names = T)

### calculate c-index
library(Hmisc)
Cindex <- rcorrcens(dataInput$status~predict(mul_glm))
SE <- Cindex[4]/2
ci95 <- paste0(round(Cindex[1]******SE,2),'-',round(Cindex[1]-1.96*SE,2));
c_index <- data.frame("C value"=Cindex[1],
                      "ci95" = ci95,
                      "p value" =Cindex[6] 
                      );c_index
write.table(c_index,file = paste0(filepath,"/2. c_index_logistic.txt"),sep = "\t",col.names = T,row.names = F)

### nomogram analysis ####
#加载诺模图和cox相关包
library(rms)
library(foreign)
library(survival)

#logistic模型多因素分析-进行比例风险假设检验
mul_glim_model[mul_glim_model$p<0.05,]
finalVar <- "age.first_admit_age+sapsii.sapsii+labsfirstday.wbc_mean+vitals_first_day.tempc_mean+vitals_first_day.spo2_mean+vitals_first_day.glucose_mean+com_fluid_electrolyte"
VarWithoutSAPSII <- "age.first_admit_age+labsfirstday.wbc_mean+vitals_first_day.tempc_mean+vitals_first_day.spo2_mean+vitals_first_day.glucose_mean+com_fluid_electrolyte"

ddist <- datadist(dataInput)
options(datadist="ddist")

fit1<-lrm(as.formula(paste0("status==1~",finalVar)),data=dataInput,x=T,y=T) 

nom <- nomogram(fit1, fun=plogis,lp=F,
                funlabel=c("30-day mortality risk"))
pdf(paste0(filepath,"/3. 30days.nomogram.pdf"))
plot(nom, xfrac=.2)
dev.off()


#install.packages("pROC")
library("pROC")
#install.packages("ResourceSelection")
library(ResourceSelection) 

mul_glm <- glm(as.formula(paste0("status~",finalVar)), data = dataInput, family = binomial())
pre <- predict(mul_glm,type='response')
roc.obj <- roc(dataInput$status,pre)
auc1 <- round(auc(dataInput$status,pre),4)

cutoff1<-coords(roc.obj, "best")
#coords(roc.obj, x="best", input="threshold", best.method="youden") # Same than last line
#ci.auc(roc.obj)

glm2 <- glm(as.formula(paste0("status~","sapsii.sapsii")), data = dataInput, family = binomial())
pre2 <- predict(glm2)
roc.obj2 <- roc(dataInput$status,pre2)
auc2 <- round(auc(dataInput$status,pre2),4)
#cutoff2<-coords(roc.obj2, "best")

mul_glm3 <- glm(as.formula(paste0("status~",VarWithoutSAPSII)), data = dataInput, family = binomial())
pre3 <- predict(mul_glm3,type='response')
roc.obj3 <- roc(dataInput$status,pre3)
auc3 <- round(auc(dataInput$status,pre3),4)

rocValues <- data.frame("model"="nomogram",
                           "AUCvalue" = as.character(auc1) , 
                           "ci95"=paste0(round(ci.auc(roc.obj)[1],3),"-",round(ci.auc(roc.obj)[3],3)),
                        'cutoff'=as.character(cutoff1[1]),
                        "cutoff_specificity"=as.character(cutoff1[2]),
                        "cutoff_sensitivity"=as.character(cutoff1[3])
                        )
write.table(rocValues,file = paste0(filepath,"/2. AUC_value_logistic.txt"),sep = "\t",col.names = T,row.names = F)

roc.list <- list(roc.obj2,roc.obj3)
pdf(paste0(filepath,"/5. ROC.logtistic.pdf"))
p<-ggroc(roc.list, aes = c("color"), legacy.axes = TRUE)+ 
  labs(x = "1 - Specificity", y = "Sensitivity", col ="Models") +
  scale_color_discrete(labels=c(paste("30-days ROC for SAPSII, AUC=",auc2),paste("30-days ROC for nomogram without SAPSII, AUC=",auc3)))+
  theme_bw() +
  theme(panel.grid.major = element_blank(),panel.grid.minor = element_blank())+
  geom_abline(color="gray",linetype="dashed")+
  theme(legend.position = c(0.7,0.15))
plot(p)
dev.off()


## 校准曲线
fit2<-lrm(as.formula(paste0("status~",finalVar)),data=dataInput,x=T,y=T) 
pred.logit <- predict(fit2)
phat <- 1/(1+exp(-pred.logit))

pdf(paste0(filepath,"/6. cal.logtistic.pdf"))
val.prob(phat, dataInput$status)  # subgroups of 20 obs.
dev.off()
                      
#cal1 <- calibrate(fit, method='boot', B=1000)
#plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))

hl <- hoslem.test(mul_glm$y,fitted(mul_glm),g=10)
hl2 <- data.frame(hl$statistic,hl$p.value)
write.table(hl2,file =paste0(filepath, "/6.1. hosmer value.txt"),row.names = F)
#### DCA curve old ####
source("stdca.R")
library("survival")
library("nricens")

## glm fit (logistic model)
mstd = glm(status ~ sapsii.sapsii+age.first_admit_age, binomial(logit), data=dataInput, x=TRUE)
mnew = glm(as.formula(paste0("status==1~",finalVar)), binomial(logit), data=dataInput, x=TRUE)
## Calculation of risk difference NRI
nri<-nribin(mdl.std = mstd, mdl.new = mnew, cut = 0.05, niter = 200,updown = 'diff')
z=abs(nri$nri[1,1]/nri$nri[1,2])
pvalue = (1-pnorm(z))*2
nri <- cbind(nri$nri[1,],pvalue)
write.table(nri, file = paste0(filepath,"/8.1m.NRI.txt"),sep="\t")

#install.packages("PredictABEL")
library(PredictABEL)
pstd= mstd$fitted.values
pnew=mnew$fitted.values
reclassification(data = dataInput,cOutcome = 46,predrisk1 = pstd,predrisk2 = pnew,cutoff = c(0,0.2,0.4,1))

# #### decision curve ####
#install.packages("rmda")
library(rmda)
# #first use rmda with the default settings (set bootstraps = 50 here to reduce computation time). 
#dataInput<-dcaData
baseline.model <- decision_curve(status~sapsii.sapsii, #fitting a logistic model
                                 data = dataInput, 
                                 study.design = "cohort", 
                                 thresholds= seq(0,1, by = 0.01),
                                 confidence.intervals =0.95,
                                 policy = "opt-in",  #default 
                                 bootstraps = 50)

# 
#plot_decision_curve(baseline.model,  curve.names = "baseline model")
full.model <- decision_curve(as.formula(paste0("status~",factorsInput)),
                             data = dataInput,
                             family = binomial(link ='logit'),
                             study.design = "cohort",
                             thresholds = seq(0,1, by = 0.01),
                             confidence.intervals= 0.95)
pdf(paste0(filepath,"/7. decisionCurve.pdf"))
#since we want to plot more than one curve, we pass a list of 'decision_curve' objects to the plot
plot_decision_curve( list(baseline.model, full.model),curve.names = c("SAPSII ", "Nomogram"), xlim = c(0, 1), ylim=c(-0.2,1),
                     confidence.intervals = FALSE,  #remove confidence intervals
                     legend.position = "topright",cost.benefit.axis =FALSE)
#plot_decision_curve(full.model,  curve.names = "full model",cost.benefit.axis =FALSE)
dev.off() 

