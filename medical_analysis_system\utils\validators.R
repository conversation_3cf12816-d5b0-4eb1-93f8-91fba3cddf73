# 医学数据分析系统 - 数据验证函数
# Medical Data Analysis System - Data Validation Functions

# 验证上传文件
validate_uploaded_file <- function(file_info) {
  errors <- c()
  warnings <- c()
  
  if (is.null(file_info)) {
    errors <- c(errors, "未选择文件")
    return(list(valid = FALSE, errors = errors, warnings = warnings))
  }
  
  # 检查文件大小
  file_size_mb <- file_info$size / (1024^2)
  if (file_size_mb > MAX_FILE_SIZE_MB) {
    errors <- c(errors, paste("文件大小超过限制 (", MAX_FILE_SIZE_MB, "MB)"))
  }
  
  # 检查文件扩展名
  file_ext <- tools::file_ext(file_info$name)
  if (!paste0(".", tolower(file_ext)) %in% SUPPORTED_FILE_TYPES) {
    errors <- c(errors, paste("不支持的文件格式:", file_ext))
  }
  
  # 检查文件名
  if (nchar(file_info$name) > 255) {
    errors <- c(errors, "文件名过长")
  }
  
  if (grepl("[<>:\"/\\|?*]", file_info$name)) {
    warnings <- c(warnings, "文件名包含特殊字符，可能导致问题")
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 验证数据完整性
validate_data_integrity <- function(data) {
  errors <- c()
  warnings <- c()
  
  if (is.null(data)) {
    errors <- c(errors, "数据为空")
    return(list(valid = FALSE, errors = errors, warnings = warnings))
  }
  
  # 检查数据维度
  if (nrow(data) == 0) {
    errors <- c(errors, "数据无行")
  }
  
  if (ncol(data) == 0) {
    errors <- c(errors, "数据无列")
  }
  
  if (ncol(data) < 2) {
    warnings <- c(warnings, "数据列数较少，可能影响分析")
  }
  
  if (nrow(data) < 30) {
    warnings <- c(warnings, "样本量较小，统计结果可能不稳定")
  }
  
  # 检查列名
  col_names <- names(data)
  if (any(is.na(col_names)) || any(col_names == "")) {
    errors <- c(errors, "存在空的列名")
  }
  
  if (any(duplicated(col_names))) {
    errors <- c(errors, "存在重复的列名")
  }
  
  # 检查特殊字符
  special_chars <- grepl("[^a-zA-Z0-9_.]", col_names)
  if (any(special_chars)) {
    warnings <- c(warnings, "列名包含特殊字符，建议使用字母、数字和下划线")
  }
  
  # 检查数据类型
  all_character <- all(sapply(data, is.character))
  if (all_character) {
    warnings <- c(warnings, "所有列都是字符型，可能需要类型转换")
  }
  
  # 检查缺失值
  total_missing <- sum(is.na(data))
  total_cells <- nrow(data) * ncol(data)
  missing_rate <- total_missing / total_cells
  
  if (missing_rate > 0.5) {
    errors <- c(errors, "缺失值比例过高 (>50%)")
  } else if (missing_rate > 0.2) {
    warnings <- c(warnings, "缺失值比例较高 (>20%)")
  }
  
  # 检查完全缺失的列
  completely_missing_cols <- sapply(data, function(x) all(is.na(x)))
  if (any(completely_missing_cols)) {
    missing_col_names <- names(data)[completely_missing_cols]
    warnings <- c(warnings, paste("以下列完全缺失:", paste(missing_col_names, collapse = ", ")))
  }
  
  # 检查常量列
  constant_cols <- sapply(data, function(x) {
    unique_vals <- unique(x[!is.na(x)])
    length(unique_vals) <= 1
  })
  
  if (any(constant_cols)) {
    constant_col_names <- names(data)[constant_cols]
    warnings <- c(warnings, paste("以下列只有单一值:", paste(constant_col_names, collapse = ", ")))
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings,
    missing_rate = missing_rate,
    constant_columns = names(data)[constant_cols],
    missing_columns = names(data)[completely_missing_cols]
  ))
}

# 验证分析变量
validate_analysis_variables <- function(data, outcome_var, covariates = NULL) {
  errors <- c()
  warnings <- c()
  
  # 检查结局变量
  if (is.null(outcome_var) || outcome_var == "") {
    errors <- c(errors, "未指定结局变量")
  } else {
    if (!outcome_var %in% names(data)) {
      errors <- c(errors, paste("结局变量不存在:", outcome_var))
    } else {
      # 检查结局变量类型
      outcome_data <- data[[outcome_var]]
      unique_values <- unique(outcome_data[!is.na(outcome_data)])
      
      if (length(unique_values) < 2) {
        errors <- c(errors, "结局变量缺少变异性")
      } else if (length(unique_values) > 2) {
        warnings <- c(warnings, "结局变量不是二分类，可能需要重新编码")
      }
      
      # 检查结局变量分布
      if (length(unique_values) == 2) {
        outcome_table <- table(outcome_data)
        min_count <- min(outcome_table)
        if (min_count < 10) {
          warnings <- c(warnings, "结局变量某类别样本量过小 (<10)")
        }
        
        # 检查不平衡
        imbalance_ratio <- max(outcome_table) / min(outcome_table)
        if (imbalance_ratio > 10) {
          warnings <- c(warnings, "结局变量严重不平衡")
        }
      }
    }
  }
  
  # 检查协变量
  if (!is.null(covariates) && length(covariates) > 0) {
    missing_covariates <- covariates[!covariates %in% names(data)]
    if (length(missing_covariates) > 0) {
      errors <- c(errors, paste("以下协变量不存在:", paste(missing_covariates, collapse = ", ")))
    }
    
    # 检查协变量与结局变量的关系
    valid_covariates <- covariates[covariates %in% names(data)]
    if (outcome_var %in% valid_covariates) {
      errors <- c(errors, "协变量中包含结局变量")
    }
    
    # 检查协变量数量
    if (length(valid_covariates) > nrow(data) / 10) {
      warnings <- c(warnings, "协变量数量相对样本量过多，可能导致过拟合")
    }
    
    # 检查协变量变异性
    constant_covariates <- c()
    for (covar in valid_covariates) {
      covar_data <- data[[covar]]
      unique_vals <- unique(covar_data[!is.na(covar_data)])
      if (length(unique_vals) <= 1) {
        constant_covariates <- c(constant_covariates, covar)
      }
    }
    
    if (length(constant_covariates) > 0) {
      warnings <- c(warnings, paste("以下协变量缺少变异性:", paste(constant_covariates, collapse = ", ")))
    }
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 验证模型参数
validate_model_parameters <- function(params) {
  errors <- c()
  warnings <- c()
  
  # 检查LASSO参数
  if ("alpha" %in% names(params)) {
    if (params$alpha < 0 || params$alpha > 1) {
      errors <- c(errors, "Alpha值必须在0-1之间")
    }
  }
  
  if ("cv_folds" %in% names(params)) {
    if (params$cv_folds < 3 || params$cv_folds > 20) {
      errors <- c(errors, "交叉验证折数应在3-20之间")
    }
  }
  
  # 检查显著性水平
  if ("alpha_level" %in% names(params)) {
    if (params$alpha_level <= 0 || params$alpha_level >= 1) {
      errors <- c(errors, "显著性水平必须在0-1之间")
    }
    if (params$alpha_level > 0.1) {
      warnings <- c(warnings, "显著性水平较高，可能增加I型错误")
    }
  }
  
  # 检查MICE参数
  if ("mice_iterations" %in% names(params)) {
    if (params$mice_iterations < 1 || params$mice_iterations > 50) {
      errors <- c(errors, "MICE迭代次数应在1-50之间")
    }
    if (params$mice_iterations < 5) {
      warnings <- c(warnings, "MICE迭代次数较少，可能影响插补质量")
    }
  }
  
  # 检查异常值阈值
  if ("outlier_threshold" %in% names(params)) {
    if (params$outlier_threshold < 1 || params$outlier_threshold > 5) {
      errors <- c(errors, "异常值阈值应在1-5之间")
    }
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 验证报告参数
validate_report_parameters <- function(params) {
  errors <- c()
  warnings <- c()
  
  # 检查报告标题
  if (is.null(params$title) || params$title == "") {
    errors <- c(errors, "报告标题不能为空")
  } else if (nchar(params$title) > 200) {
    errors <- c(errors, "报告标题过长")
  }
  
  # 检查作者
  if (is.null(params$author) || params$author == "") {
    warnings <- c(warnings, "未指定报告作者")
  }
  
  # 检查输出格式
  if (is.null(params$formats) || length(params$formats) == 0) {
    errors <- c(errors, "未选择输出格式")
  } else {
    valid_formats <- c("html", "pdf", "docx")
    invalid_formats <- params$formats[!params$formats %in% valid_formats]
    if (length(invalid_formats) > 0) {
      errors <- c(errors, paste("不支持的输出格式:", paste(invalid_formats, collapse = ", ")))
    }
  }
  
  # 检查包含内容
  content_options <- c("data_summary", "descriptive", "univariate", "multivariate", 
                      "lasso", "plots", "model_evaluation")
  selected_content <- params[content_options]
  selected_content <- selected_content[!is.na(selected_content) & selected_content == TRUE]
  
  if (length(selected_content) == 0) {
    errors <- c(errors, "未选择任何报告内容")
  }
  
  return(list(
    valid = length(errors) == 0,
    errors = errors,
    warnings = warnings
  ))
}

# 综合验证函数
comprehensive_validation <- function(data, params) {
  all_errors <- c()
  all_warnings <- c()
  
  # 数据完整性验证
  data_validation <- validate_data_integrity(data)
  all_errors <- c(all_errors, data_validation$errors)
  all_warnings <- c(all_warnings, data_validation$warnings)
  
  # 分析变量验证
  if (!is.null(params$outcome_var)) {
    var_validation <- validate_analysis_variables(data, params$outcome_var, params$covariates)
    all_errors <- c(all_errors, var_validation$errors)
    all_warnings <- c(all_warnings, var_validation$warnings)
  }
  
  # 模型参数验证
  param_validation <- validate_model_parameters(params)
  all_errors <- c(all_errors, param_validation$errors)
  all_warnings <- c(all_warnings, param_validation$warnings)
  
  return(list(
    valid = length(all_errors) == 0,
    errors = unique(all_errors),
    warnings = unique(all_warnings),
    data_quality = data_validation
  ))
}
