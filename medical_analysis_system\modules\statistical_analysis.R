# 医学数据分析系统 - 统计分析模块
# Medical Data Analysis System - Statistical Analysis Module

# 单因素logistic回归分析
perform_univariate_analysis <- function(data, outcome_var, covariates = NULL) {
  tryCatch({
    log_info("开始单因素分析")
    
    # 如果未指定协变量，使用除结局变量外的所有变量
    if (is.null(covariates)) {
      covariates <- names(data)[names(data) != outcome_var]
    }
    
    # 移除只有一个唯一值的变量
    single_value_vars <- sapply(data[covariates], function(x) length(unique(x[!is.na(x)])) <= 1)
    if (any(single_value_vars)) {
      excluded_vars <- names(single_value_vars)[single_value_vars]
      log_warn(paste("排除只有单一值的变量:", paste(excluded_vars, collapse = ", ")))
      covariates <- covariates[!covariates %in% excluded_vars]
    }
    
    # 单因素分析函数
    univariate_glm <- function(var) {
      tryCatch({
        formula_str <- paste0(outcome_var, " ~ ", var)
        model <- glm(as.formula(formula_str), data = data, family = binomial())
        
        # 提取结果
        summary_model <- summary(model)
        coef_table <- summary_model$coefficients
        
        if (nrow(coef_table) > 1) {  # 确保有协变量系数
          coef_row <- coef_table[2, ]  # 第二行是协变量系数
          
          OR <- exp(coef_row[1])
          SE <- coef_row[2]
          CI_lower <- exp(coef_row[1] - 1.96 * SE)
          CI_upper <- exp(coef_row[1] + 1.96 * SE)
          p_value <- coef_row[4]
          
          # 显著性标记
          p_star <- ifelse(p_value < 0.001, "***",
                          ifelse(p_value < 0.01, "**",
                                ifelse(p_value < 0.05, "*", "")))
          
          return(data.frame(
            variable = var,
            OR = round(OR, 4),
            CI_lower = round(CI_lower, 4),
            CI_upper = round(CI_upper, 4),
            CI = paste0(round(CI_lower, 4), "-", round(CI_upper, 4)),
            p_value = round(p_value, 4),
            significance = p_star,
            stringsAsFactors = FALSE
          ))
        } else {
          return(NULL)
        }
      }, error = function(e) {
        log_warn(paste("变量", var, "单因素分析失败:", e$message))
        return(NULL)
      })
    }
    
    # 对所有协变量进行单因素分析
    results_list <- lapply(covariates, univariate_glm)
    
    # 合并结果
    results <- do.call(rbind, results_list[!sapply(results_list, is.null)])
    
    if (nrow(results) > 0) {
      # 按p值排序
      results <- results[order(results$p_value), ]
      rownames(results) <- NULL
      
      log_info(paste("单因素分析完成，共分析", nrow(results), "个变量"))
      return(results)
    } else {
      log_warn("单因素分析未产生有效结果")
      return(NULL)
    }
    
  }, error = function(e) {
    log_error(paste("单因素分析失败:", e$message))
    stop(paste("单因素分析失败:", e$message))
  })
}

# LASSO回归变量选择
perform_lasso_analysis <- function(data, outcome_var, alpha = 1, cv_folds = 5) {
  tryCatch({
    log_info("开始LASSO回归分析")
    
    library(glmnet)
    
    # 准备数据
    covariates <- names(data)[names(data) != outcome_var]
    
    # 移除非数值变量或转换为数值
    x_data <- data[, covariates, drop = FALSE]
    
    # 处理分类变量
    for (col in names(x_data)) {
      if (is.character(x_data[[col]]) || is.factor(x_data[[col]])) {
        x_data[[col]] <- as.numeric(as.factor(x_data[[col]]))
      }
    }
    
    # 转换为矩阵
    x <- as.matrix(x_data)
    y <- as.numeric(data[[outcome_var]])
    
    # 检查数据
    if (any(is.na(x)) || any(is.na(y))) {
      stop("数据中存在缺失值，请先进行数据清洗")
    }
    
    # LASSO回归
    lasso_model <- glmnet(x, y, family = "binomial", alpha = alpha)
    
    # 交叉验证选择最优lambda
    cv_lasso <- cv.glmnet(x, y, family = "binomial", alpha = alpha, 
                         nfolds = cv_folds, type.measure = "class")
    
    # 提取系数
    optimal_lambda <- cv_lasso$lambda.1se
    lasso_coef <- coef(lasso_model, s = optimal_lambda)
    
    # 选择的变量
    selected_vars <- rownames(lasso_coef)[lasso_coef[, 1] != 0]
    selected_vars <- selected_vars[selected_vars != "(Intercept)"]
    
    # 构建结果
    lasso_results <- list(
      model = lasso_model,
      cv_model = cv_lasso,
      optimal_lambda = optimal_lambda,
      selected_variables = selected_vars,
      coefficients = as.matrix(lasso_coef),
      n_selected = length(selected_vars)
    )
    
    log_info(paste("LASSO回归完成，选择了", length(selected_vars), "个变量"))
    return(lasso_results)
    
  }, error = function(e) {
    log_error(paste("LASSO回归分析失败:", e$message))
    stop(paste("LASSO回归分析失败:", e$message))
  })
}

# 多因素logistic回归分析
perform_multivariate_analysis <- function(data, outcome_var, selected_vars = NULL, 
                                        method = "enter") {
  tryCatch({
    log_info("开始多因素分析")
    
    # 如果未指定变量，使用所有变量
    if (is.null(selected_vars)) {
      selected_vars <- names(data)[names(data) != outcome_var]
    }
    
    # 移除只有一个唯一值的变量
    single_value_vars <- sapply(data[selected_vars], function(x) length(unique(x[!is.na(x)])) <= 1)
    if (any(single_value_vars)) {
      excluded_vars <- names(single_value_vars)[single_value_vars]
      log_warn(paste("排除只有单一值的变量:", paste(excluded_vars, collapse = ", ")))
      selected_vars <- selected_vars[!selected_vars %in% excluded_vars]
    }
    
    if (length(selected_vars) == 0) {
      stop("没有可用于多因素分析的变量")
    }
    
    # 构建公式
    formula_str <- paste0(outcome_var, " ~ ", paste(selected_vars, collapse = " + "))
    
    # 拟合模型
    multi_model <- glm(as.formula(formula_str), data = data, family = binomial())
    
    # 提取结果
    library(broom)
    model_summary <- summary(multi_model)
    coef_table <- model_summary$coefficients
    
    if (nrow(coef_table) > 1) {
      # 计算OR和置信区间
      OR <- exp(coef_table[-1, 1])  # 排除截距
      
      # 计算置信区间
      confint_result <- tryCatch({
        confint(multi_model)[-1, ]  # 排除截距
      }, error = function(e) {
        # 如果confint失败，使用Wald方法
        coef_vals <- coef_table[-1, 1]
        se_vals <- coef_table[-1, 2]
        cbind(coef_vals - 1.96 * se_vals, coef_vals + 1.96 * se_vals)
      })
      
      CI_lower <- exp(confint_result[, 1])
      CI_upper <- exp(confint_result[, 2])
      
      p_values <- coef_table[-1, 4]
      
      # 显著性标记
      p_star <- ifelse(p_values < 0.001, "***",
                      ifelse(p_values < 0.01, "**",
                            ifelse(p_values < 0.05, "*", "")))
      
      # 构建结果数据框
      results <- data.frame(
        variable = rownames(coef_table)[-1],
        OR = round(OR, 4),
        CI_lower = round(CI_lower, 4),
        CI_upper = round(CI_upper, 4),
        CI = paste0(round(CI_lower, 4), "-", round(CI_upper, 4)),
        p_value = round(p_values, 4),
        significance = p_star,
        stringsAsFactors = FALSE
      )
      
      # 模型评估指标
      model_metrics <- list(
        AIC = AIC(multi_model),
        BIC = BIC(multi_model),
        deviance = multi_model$deviance,
        null_deviance = multi_model$null.deviance,
        df_residual = multi_model$df.residual
      )
      
      # 返回完整结果
      multi_results <- list(
        model = multi_model,
        results_table = results,
        model_metrics = model_metrics,
        formula = formula_str
      )
      
      log_info(paste("多因素分析完成，包含", nrow(results), "个变量"))
      return(multi_results)
      
    } else {
      stop("多因素模型拟合失败")
    }
    
  }, error = function(e) {
    log_error(paste("多因素分析失败:", e$message))
    stop(paste("多因素分析失败:", e$message))
  })
}

# 模型性能评估
evaluate_model_performance <- function(model, data, outcome_var) {
  tryCatch({
    log_info("开始模型性能评估")
    
    library(pROC)
    library(Hmisc)
    library(ResourceSelection)
    
    # 预测概率
    predicted_prob <- predict(model, type = "response")
    actual_outcome <- data[[outcome_var]]
    
    # ROC分析
    roc_obj <- roc(actual_outcome, predicted_prob)
    auc_value <- auc(roc_obj)
    auc_ci <- ci.auc(roc_obj)
    
    # 最佳截断值
    best_threshold <- coords(roc_obj, "best")
    
    # C-index计算
    c_index <- rcorrcens(actual_outcome ~ predicted_prob)
    
    # Hosmer-Lemeshow拟合优度检验
    hl_test <- hoslem.test(actual_outcome, predicted_prob, g = 10)
    
    # 分类性能指标
    predicted_class <- ifelse(predicted_prob > best_threshold$threshold, 1, 0)
    
    # 混淆矩阵
    confusion_matrix <- table(Predicted = predicted_class, Actual = actual_outcome)
    
    if (nrow(confusion_matrix) == 2 && ncol(confusion_matrix) == 2) {
      TP <- confusion_matrix[2, 2]
      TN <- confusion_matrix[1, 1]
      FP <- confusion_matrix[2, 1]
      FN <- confusion_matrix[1, 2]
      
      sensitivity <- TP / (TP + FN)
      specificity <- TN / (TN + FP)
      ppv <- TP / (TP + FP)
      npv <- TN / (TN + FN)
      accuracy <- (TP + TN) / sum(confusion_matrix)
    } else {
      sensitivity <- specificity <- ppv <- npv <- accuracy <- NA
    }
    
    # 构建评估结果
    performance_metrics <- list(
      # ROC相关
      auc = round(as.numeric(auc_value), 4),
      auc_ci_lower = round(as.numeric(auc_ci[1]), 4),
      auc_ci_upper = round(as.numeric(auc_ci[3]), 4),
      auc_ci = paste0(round(as.numeric(auc_ci[1]), 4), "-", round(as.numeric(auc_ci[3]), 4)),
      
      # 最佳截断值
      best_threshold = round(best_threshold$threshold, 4),
      threshold_sensitivity = round(best_threshold$sensitivity, 4),
      threshold_specificity = round(best_threshold$specificity, 4),
      
      # C-index
      c_index = round(c_index["C Index"], 4),
      c_index_se = round(c_index["S.D."]/2, 4),
      
      # 分类性能
      sensitivity = round(sensitivity, 4),
      specificity = round(specificity, 4),
      ppv = round(ppv, 4),
      npv = round(npv, 4),
      accuracy = round(accuracy, 4),
      
      # 拟合优度
      hosmer_lemeshow_stat = round(hl_test$statistic, 4),
      hosmer_lemeshow_p = round(hl_test$p.value, 4),
      
      # 其他
      roc_object = roc_obj,
      confusion_matrix = confusion_matrix
    )
    
    log_info("模型性能评估完成")
    return(performance_metrics)
    
  }, error = function(e) {
    log_error(paste("模型性能评估失败:", e$message))
    stop(paste("模型性能评估失败:", e$message))
  })
}

# 描述性统计分析
perform_descriptive_analysis <- function(data, group_var = NULL) {
  tryCatch({
    log_info("开始描述性统计分析")
    
    library(tableone)
    
    # 确定变量类型
    numeric_vars <- names(data)[sapply(data, is.numeric)]
    categorical_vars <- names(data)[sapply(data, function(x) is.character(x) || is.factor(x))]
    
    # 创建Table 1
    if (!is.null(group_var) && group_var %in% names(data)) {
      # 分组描述性统计
      vars_to_include <- names(data)[names(data) != group_var]
      
      table_one <- CreateTableOne(
        vars = vars_to_include,
        strata = group_var,
        data = data,
        factorVars = categorical_vars[categorical_vars != group_var]
      )
    } else {
      # 整体描述性统计
      table_one <- CreateTableOne(
        vars = names(data),
        data = data,
        factorVars = categorical_vars
      )
    }
    
    # 转换为数据框
    table_one_df <- print(table_one, showAllLevels = TRUE, cramVars = categorical_vars)
    
    log_info("描述性统计分析完成")
    return(list(
      table_one = table_one,
      table_one_df = table_one_df,
      numeric_vars = numeric_vars,
      categorical_vars = categorical_vars
    ))
    
  }, error = function(e) {
    log_error(paste("描述性统计分析失败:", e$message))
    stop(paste("描述性统计分析失败:", e$message))
  })
}

# 变量重要性分析
analyze_variable_importance <- function(univariate_results, multivariate_results,
                                      lasso_results = NULL) {
  tryCatch({
    log_info("开始变量重要性分析")

    # 获取所有变量
    all_vars <- c()
    if (!is.null(univariate_results)) all_vars <- c(all_vars, univariate_results$variable)
    if (!is.null(multivariate_results$results_table)) all_vars <- c(all_vars, multivariate_results$results_table$variable)
    if (!is.null(lasso_results$selected_variables)) all_vars <- c(all_vars, lasso_results$selected_variables)
    all_vars <- unique(all_vars)

    importance_df <- data.frame(
      variable = all_vars,
      univariate_p = NA,
      univariate_or = NA,
      multivariate_p = NA,
      multivariate_or = NA,
      lasso_selected = FALSE,
      importance_score = 0,
      stringsAsFactors = FALSE
    )

    # 填充单因素结果
    if (!is.null(univariate_results)) {
      for (i in 1:nrow(importance_df)) {
        var <- importance_df$variable[i]
        uni_row <- univariate_results[univariate_results$variable == var, ]
        if (nrow(uni_row) > 0) {
          importance_df$univariate_p[i] <- uni_row$p_value[1]
          importance_df$univariate_or[i] <- uni_row$OR[1]
        }
      }
    }

    # 填充多因素结果
    if (!is.null(multivariate_results$results_table)) {
      for (i in 1:nrow(importance_df)) {
        var <- importance_df$variable[i]
        multi_row <- multivariate_results$results_table[multivariate_results$results_table$variable == var, ]
        if (nrow(multi_row) > 0) {
          importance_df$multivariate_p[i] <- multi_row$p_value[1]
          importance_df$multivariate_or[i] <- multi_row$OR[1]
        }
      }
    }

    # 填充LASSO结果
    if (!is.null(lasso_results$selected_variables)) {
      importance_df$lasso_selected <- importance_df$variable %in% lasso_results$selected_variables
    }

    # 计算重要性评分
    for (i in 1:nrow(importance_df)) {
      score <- 0

      # 单因素显著性评分
      if (!is.na(importance_df$univariate_p[i])) {
        if (importance_df$univariate_p[i] < 0.001) score <- score + 4
        else if (importance_df$univariate_p[i] < 0.01) score <- score + 3
        else if (importance_df$univariate_p[i] < 0.05) score <- score + 2
      }

      # 多因素显著性评分
      if (!is.na(importance_df$multivariate_p[i])) {
        if (importance_df$multivariate_p[i] < 0.001) score <- score + 7
        else if (importance_df$multivariate_p[i] < 0.01) score <- score + 5
        else if (importance_df$multivariate_p[i] < 0.05) score <- score + 3
      }

      # LASSO选择评分
      if (importance_df$lasso_selected[i]) score <- score + 3

      importance_df$importance_score[i] <- score
    }

    # 按重要性评分排序
    importance_df <- importance_df[order(importance_df$importance_score, decreasing = TRUE), ]
    rownames(importance_df) <- NULL

    log_info("变量重要性分析完成")
    return(importance_df)

  }, error = function(e) {
    log_error(paste("变量重要性分析失败:", e$message))
    stop(paste("变量重要性分析失败:", e$message))
  })
}
