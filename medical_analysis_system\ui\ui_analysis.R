# 医学数据分析系统 - 分析配置界面
# Medical Data Analysis System - Analysis Configuration UI

# 描述性统计界面
ui_descriptive <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #00b894 0%, #00a085 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("chart-pie", style = "margin-right: 15px;"),
          "描述性统计",
          style = "margin: 0; font-weight: 300;"
        ),
        p("生成数据的基本统计描述", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(4,
      box(
        title = "分析设置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "desc_group_var",
            "分组变量（可选）",
            choices = NULL,
            selected = NULL
          ),
          
          checkboxGroupInput(
            "desc_variables",
            "选择变量",
            choices = NULL
          ),

          div(
            style = "margin-top: 10px; margin-bottom: 15px;",
            actionButton(
              "desc_select_all",
              "全选",
              icon = icon("check-square"),
              class = "btn-sm btn-outline-primary",
              style = "margin-right: 5px;"
            ),
            actionButton(
              "desc_select_none",
              "反选",
              icon = icon("square"),
              class = "btn-sm btn-outline-secondary"
            )
          ),
          
          hr(),
          
          h5("输出选项"),
          checkboxInput("desc_show_missing", "显示缺失值", value = TRUE),
          checkboxInput("desc_show_normal_test", "正态性检验", value = FALSE),
          checkboxInput("desc_show_variance_test", "方差齐性检验", value = FALSE),
          
          hr(),
          
          actionButton(
            "run_descriptive",
            "生成描述性统计",
            icon = icon("play"),
            class = "btn-primary",
            style = "width: 100%;"
          )
        )
      )
    ),
    
    column(8,
      box(
        title = "统计结果",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          DT::dataTableOutput("descriptive_table")
        )
      )
    )
  )
)

# 单因素分析界面
ui_univariate <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("search", style = "margin-right: 15px;"),
          "单因素分析",
          style = "margin: 0; font-weight: 300;"
        ),
        p("对每个变量进行单独的统计分析", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(4,
      box(
        title = "分析设置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "uni_outcome_var",
            "结局变量",
            choices = NULL,
            selected = NULL
          ),
          
          checkboxGroupInput(
            "uni_covariates",
            "协变量",
            choices = NULL
          ),

          div(
            style = "margin-top: 10px; margin-bottom: 15px;",
            actionButton(
              "uni_select_all",
              "全选",
              icon = icon("check-square"),
              class = "btn-sm btn-outline-primary",
              style = "margin-right: 5px;"
            ),
            actionButton(
              "uni_select_none",
              "反选",
              icon = icon("square"),
              class = "btn-sm btn-outline-secondary"
            )
          ),
          
          hr(),
          
          h5("分析选项"),
          numericInput(
            "uni_alpha_level",
            "显著性水平",
            value = 0.05,
            min = 0.001,
            max = 0.1,
            step = 0.001
          ),
          
          checkboxInput("uni_adjust_multiple", "多重比较校正", value = FALSE),
          
          conditionalPanel(
            condition = "input.uni_adjust_multiple",
            selectInput(
              "uni_adjust_method",
              "校正方法",
              choices = list(
                "Bonferroni" = "bonferroni",
                "Benjamini-Hochberg" = "BH",
                "Benjamini-Yekutieli" = "BY"
              ),
              selected = "BH"
            )
          ),
          
          hr(),
          
          actionButton(
            "run_univariate",
            "开始单因素分析",
            icon = icon("play"),
            class = "btn-primary",
            style = "width: 100%;"
          )
        )
      )
    ),
    
    column(8,
      box(
        title = "分析结果",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "结果表格",
              div(
                style = "margin-top: 15px;",
                DT::dataTableOutput("univariate_table")
              )
            ),
            
            tabPanel(
              "森林图",
              div(
                style = "margin-top: 15px;",
                plotOutput("univariate_forest_plot", height = "600px")
              )
            ),
            
            tabPanel(
              "火山图",
              div(
                style = "margin-top: 15px;",
                plotOutput("univariate_volcano_plot", height = "500px")
              )
            )
          )
        )
      )
    )
  )
)

# 多因素分析界面
ui_multivariate <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("layer-group", style = "margin-right: 15px;"),
          "多因素分析",
          style = "margin: 0; font-weight: 300;"
        ),
        p("同时考虑多个变量的综合分析", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(4,
      box(
        title = "分析设置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "multi_outcome_var",
            "结局变量",
            choices = NULL,
            selected = NULL
          ),
          
          checkboxGroupInput(
            "multi_covariates",
            "协变量",
            choices = NULL
          ),

          div(
            style = "margin-top: 10px; margin-bottom: 15px;",
            actionButton(
              "multi_select_all",
              "全选",
              icon = icon("check-square"),
              class = "btn-sm btn-outline-primary",
              style = "margin-right: 5px;"
            ),
            actionButton(
              "multi_select_none",
              "反选",
              icon = icon("square"),
              class = "btn-sm btn-outline-secondary"
            )
          ),
          
          hr(),
          
          h5("变量选择方法"),
          radioButtons(
            "multi_selection_method",
            NULL,
            choices = list(
              "强制进入" = "enter",
              "前进法" = "forward",
              "后退法" = "backward",
              "逐步法" = "stepwise",
              "使用LASSO结果" = "lasso"
            ),
            selected = "enter"
          ),
          
          conditionalPanel(
            condition = "input.multi_selection_method != 'enter' && input.multi_selection_method != 'lasso'",
            numericInput(
              "multi_entry_p",
              "进入P值",
              value = 0.05,
              min = 0.001,
              max = 0.2,
              step = 0.001
            ),
            numericInput(
              "multi_removal_p",
              "剔除P值",
              value = 0.1,
              min = 0.001,
              max = 0.2,
              step = 0.001
            )
          ),
          
          hr(),
          
          actionButton(
            "run_multivariate",
            "开始多因素分析",
            icon = icon("play"),
            class = "btn-primary",
            style = "width: 100%;"
          )
        )
      )
    ),
    
    column(8,
      box(
        title = "分析结果",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "结果表格",
              div(
                style = "margin-top: 15px;",
                DT::dataTableOutput("multivariate_table")
              )
            ),
            
            tabPanel(
              "森林图",
              div(
                style = "margin-top: 15px;",
                plotOutput("multivariate_forest_plot", height = "600px")
              )
            ),
            
            tabPanel(
              "模型诊断",
              div(
                style = "margin-top: 15px;",
                fluidRow(
                  column(6, plotOutput("model_residuals_plot")),
                  column(6, plotOutput("model_influence_plot"))
                )
              )
            )
          )
        )
      )
    )
  )
)

# LASSO回归界面
ui_lasso <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #e17055 0%, #d63031 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("filter", style = "margin-right: 15px;"),
          "LASSO回归",
          style = "margin: 0; font-weight: 300;"
        ),
        p("使用LASSO方法进行变量选择和正则化", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(4,
      box(
        title = "分析设置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          selectInput(
            "lasso_outcome_var",
            "结局变量",
            choices = NULL,
            selected = NULL
          ),
          
          checkboxGroupInput(
            "lasso_covariates",
            "候选变量",
            choices = NULL
          ),

          div(
            style = "margin-top: 10px; margin-bottom: 15px;",
            actionButton(
              "lasso_select_all",
              "全选",
              icon = icon("check-square"),
              class = "btn-sm btn-outline-primary",
              style = "margin-right: 5px;"
            ),
            actionButton(
              "lasso_select_none",
              "反选",
              icon = icon("square"),
              class = "btn-sm btn-outline-secondary"
            )
          ),
          
          hr(),
          
          h5("LASSO参数"),
          numericInput(
            "lasso_alpha",
            "Alpha值",
            value = 1,
            min = 0,
            max = 1,
            step = 0.1
          ),
          
          numericInput(
            "lasso_cv_folds",
            "交叉验证折数",
            value = 5,
            min = 3,
            max = 10,
            step = 1
          ),
          
          selectInput(
            "lasso_lambda_selection",
            "Lambda选择",
            choices = list(
              "1SE规则" = "lambda.1se",
              "最小值" = "lambda.min"
            ),
            selected = "lambda.1se"
          ),
          
          hr(),
          
          actionButton(
            "run_lasso",
            "开始LASSO分析",
            icon = icon("play"),
            class = "btn-primary",
            style = "width: 100%;"
          )
        )
      )
    ),
    
    column(8,
      box(
        title = "分析结果",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "选择的变量",
              div(
                style = "margin-top: 15px;",
                DT::dataTableOutput("lasso_selected_vars")
              )
            ),
            
            tabPanel(
              "系数路径",
              div(
                style = "margin-top: 15px;",
                plotOutput("lasso_path_plot", height = "500px")
              )
            ),
            
            tabPanel(
              "交叉验证",
              div(
                style = "margin-top: 15px;",
                plotOutput("lasso_cv_plot", height = "500px")
              )
            )
          )
        )
      )
    )
  )
)

# Logistic回归界面
ui_logistic <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
                 border-radius: 10px; color: white;",
        h2(
          icon("calculator", style = "margin-right: 15px;"),
          "Logistic回归",
          style = "margin: 0; font-weight: 300;"
        ),
        p("构建二分类预测模型", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),

  fluidRow(
    column(12,
      box(
        title = "Logistic回归建模",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 20px; text-align: center;",
          h4("功能开发中..."),
          p("Logistic回归建模功能正在开发中，敬请期待！")
        )
      )
    )
  )
)

# 生存分析界面
ui_survival <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #00cec9 0%, #55a3ff 100%);
                 border-radius: 10px; color: white;",
        h2(
          icon("heartbeat", style = "margin-right: 15px;"),
          "生存分析",
          style = "margin: 0; font-weight: 300;"
        ),
        p("时间-事件分析", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),

  fluidRow(
    column(12,
      box(
        title = "生存分析",
        status = "info",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 20px; text-align: center;",
          h4("功能开发中..."),
          p("生存分析功能正在开发中，敬请期待！")
        )
      )
    )
  )
)

# 列线图界面
ui_nomogram <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
                 border-radius: 10px; color: white;",
        h2(
          icon("chart-area", style = "margin-right: 15px;"),
          "列线图",
          style = "margin: 0; font-weight: 300;"
        ),
        p("预测模型可视化工具", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),

  fluidRow(
    column(12,
      box(
        title = "列线图构建",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 20px; text-align: center;",
          h4("功能开发中..."),
          p("列线图构建功能正在开发中，敬请期待！")
        )
      )
    )
  )
)
