set.seed(124)
library(ggplot2); theme_set(theme_bw(base_size = 14))

dat <- lme4::InstEval
dat <- dat[sample(nrow(dat), 1000), ]

fit <- glm(y > 3 ~ studage + lectage + service + dept, binomial, dat)
pdat <- with(dat, data.frame(y = ifelse(y > 3, 1, 0),
                             prob = predict(fit, type = "response")))
ggplot(pdat, aes(prob, y)) +
  geom_point(shape = 21, size = 2) +
  geom_abline(slope = 1, intercept = 0) +
  geom_smooth(method = stats::loess, se = FALSE) +
  scale_x_continuous(breaks = seq(0, 1, 0.1)) +
  scale_y_continuous(breaks = seq(0, 1, 0.1)) +
  xlab("Estimated Prob.") +
  ylab("Data w/ Empirical Prob.") +
  ggtitle("Logistic Regression Calibration Plot")


library(rms)

refit <- lrm(y > 3 ~ studage + lectage + service + dept, dat, x = TRUE, y = TRUE)
plot(calibrate(refit, B = 400))

#=================
fit2<-lrm(as.formula(paste0("status~",finalVar)),data=dataInput,x=T,y=T) 
pred.logit <- predict(fit2)
phat <- 1/(1+exp(-pred.logit))

pdf(paste0(filepath,"/6. cal.logtistic.pdf"))
val.prob(phat, dataInput$status)  # subgroups of 20 obs.
dev.off()

plot(calibrate(fit2, method='boot', B = 400))

sub_lenght = dim(dataInput)[1]/2
fit<-lrm(as.formula(paste0("status~",finalVar)),data=dataInput, subset=1:sub_lenght,x=T,y=T) 
pred.logit <- predict(fit,dataInput[sub_lenght+1:sub_lenght*2,])
phat <- 1/(1+exp(-pred.logit))

val.prob(phat, dataInput$status[sub_lenght+1:sub_lenght*2], m=100, cex=.5)  # subgroups of 20 obs.

#==============
set.seed(1)
n <- 200
x1 <- runif(n)
x2 <- runif(n)
x3 <- runif(n)
logit <- 2*(x1-.5)
P <- 1/(1+exp(-logit))
y <- ifelse(runif(n)<=P, 1, 0)
d <- data.frame(x1,x2,x3,y)


f <- lrm(y ~ x1 + x2 + x3, subset=1:100)
pred.logit <- predict(f, d[101:200,])
phat <- 1/(1+exp(-pred.logit))
val.prob(phat, y[101:200], m=20, cex=.5)  # subgroups of 20 obs.


############
测试集和验证集要分开。

